<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="yq-mars" />
      <option name="name" value="yq-mars" />
      <option name="url" value="https://packages.aliyun.com/61715dae10204867ecfd5397/maven/yq-mars" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="snapshots" />
      <option name="name" value="snapshots" />
      <option name="url" value="https://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
  </component>
</project>