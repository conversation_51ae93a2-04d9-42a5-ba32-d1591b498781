{
    "java.configuration.updateBuildConfiguration": "interactive",
    "java.configuration.maven.userSettings": "D:\\work\\server\\apache-maven-3.9.9\\conf\\settings.xml",
    "java.configuration.maven.globalSettings": "D:\\work\\server\\apache-maven-3.9.9\\conf\\settings.xml",
    "maven.executable.path": "D:\\work\\server\\apache-maven-3.9.9\\bin\\mvn.cmd",
    "java.jdt.ls.java.home": "D:\\Users\\46365\\graalvm-jdk-21.0.6",
    "workbench.tree.indent": 14,
    "java.debug.settings.exceptionBreakpoint.skipClasses": [
}