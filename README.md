# GT06协议服务器

基于Java开发的GT06物联网设备通信协议服务器，支持车载GPS定位器与服务器平台之间的数据交互。

## 项目特性

- 完整的GT06协议实现
- 支持多设备并发连接
- 协议包自动解析和响应
- CRC-ITU校验算法
- 设备会话管理
- 日志记录和监控

## 支持的协议包类型

- **登录包 (0x01)**: 设备登录认证
- **定位数据包 (0x12)**: GPS定位信息上报
- **心跳包 (0x13)**: 设备状态信息
- **报警包 (0x16)**: 各种报警信息
- **指令响应包 (0x81)**: 设备指令执行结果

## 项目结构

```
gt06-protocol-server/
├── src/main/java/com/gt06/
│   ├── server/          # TCP服务器核心
│   │   └── GT06Server.java
│   ├── protocol/        # 协议解析器
│   │   ├── ProtocolDecoder.java
│   │   └── ProtocolEncoder.java
│   ├── handler/         # 消息处理器
│   │   └── PacketHandler.java
│   ├── model/           # 数据模型
│   │   ├── ProtocolPacket.java
│   │   ├── LoginPacket.java
│   │   ├── LocationPacket.java
│   │   ├── HeartbeatPacket.java
│   │   ├── AlarmPacket.java
│   │   └── CommandResponsePacket.java
│   ├── util/            # 工具类
│   │   └── CrcUtil.java
│   └── Application.java # 启动类
├── pom.xml              # Maven配置
└── README.md            # 项目说明
```

## 快速开始

### 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本

### 编译项目

```bash
mvn clean compile
```

### 运行服务器

```bash
# 使用默认端口8080
java -cp target/classes com.gt06.Application

# 指定端口
java -cp target/classes com.gt06.Application 8841
```

### 使用Maven运行

```bash
mvn exec:java -Dexec.mainClass="com.gt06.Application" -Dexec.args="8841"
```

## 协议说明

### 数据包格式

```
起始位(2字节) + 包长度(1字节) + 协议号(1字节) + 信息内容(N字节) + 信息序列号(2字节) + CRC校验(2字节) + 停止位(2字节)
```

- **起始位**: 固定为 `0x78 0x78`
- **包长度**: 从协议号到CRC校验的字节数
- **协议号**: 标识数据包类型
- **信息内容**: 具体的数据内容
- **信息序列号**: 数据包序号
- **CRC校验**: CRC-ITU校验值
- **停止位**: 固定为 `0x0D 0x0A`

### 主要协议号

| 协议号 | 名称 | 说明 |
|--------|------|------|
| 0x01 | 登录包 | 设备登录服务器 |
| 0x12 | 定位数据包 | GPS定位信息 |
| 0x13 | 心跳包 | 设备状态信息 |
| 0x16 | 报警包 | 各种报警信息 |
| 0x80 | 指令下发 | 服务器向设备发送指令 |
| 0x81 | 指令响应 | 设备响应服务器指令 |

## 开发指南

### 添加新的协议包类型

1. 在 `model` 包中创建新的协议包类，继承 `ProtocolPacket`
2. 实现 `getInfoContent()` 和 `parseFromBytes()` 方法
3. 在 `ProtocolDecoder` 中添加解码逻辑
4. 在 `PacketHandler` 中添加处理逻辑

### 自定义数据处理

修改 `PacketHandler` 类中的相应方法：
- `storeLocationData()`: 定位数据存储
- `storeHeartbeatData()`: 心跳数据存储
- `processAlarm()`: 报警处理
- `processCommandResponse()`: 指令响应处理

## 配置说明

### 服务器配置

在 `GT06Server` 类中可以调整以下参数：
- 端口号
- 线程池大小
- 连接超时时间
- Socket参数

### 日志配置

项目使用SLF4J日志框架，可以通过logback.xml配置日志级别和输出格式。

## 测试

### 单元测试

```bash
mvn test
```

### 集成测试

可以使用TCP客户端工具（如telnet、netcat）连接服务器进行测试：

```bash
telnet localhost 8841
```

## 部署

### 打包

```bash
mvn clean package
```

### 运行JAR包

```bash
java -jar target/gt06-protocol-server-1.0.0.jar 8841
```

## 企业级功能特性

### 实时监控系统

GT06服务器内置了完整的实时监控系统，提供以下监控能力：

- **连接监控**: 实时跟踪设备连接数、活跃连接、连接成功率
- **性能监控**: QPS统计、处理时间分析、吞吐量监控
- **错误监控**: CRC错误、解码错误、连接错误统计
- **资源监控**: 内存使用率、线程池状态、GC统计
- **协议监控**: 各类协议包统计和分析

### 智能告警系统

基于规则引擎的智能告警系统，支持多种告警策略：

- **阈值告警**: 错误率、连接数、处理时间等指标超阈值告警
- **趋势告警**: 基于历史数据的趋势分析告警
- **异常告警**: 系统异常和故障自动告警
- **告警级别**: 支持INFO、WARNING、ERROR、CRITICAL四个级别
- **告警通知**: 支持日志、邮件、短信等多种通知方式

### 容错与恢复机制

企业级的容错和自动恢复能力：

- **连接容错**: 自动重连、连接池管理、连接超时处理
- **数据容错**: CRC校验、数据重传、错误数据过滤
- **系统容错**: 异常捕获、优雅降级、服务隔离
- **自动恢复**: 故障自动检测和恢复、健康检查
- **熔断机制**: 防止级联故障的熔断保护

### 性能优化引擎

自动化的性能优化和资源管理：

- **线程池优化**: 动态调整线程池大小、任务队列管理
- **内存优化**: 自动垃圾回收建议、内存泄漏检测
- **连接优化**: 连接池管理、空闲连接清理
- **缓存优化**: LRU缓存、过期数据清理
- **JVM优化**: GC调优建议、堆内存管理

### Web管理控制台

功能丰富的Web管理界面：

- **实时仪表板**: 系统状态总览、关键指标展示
- **监控图表**: 历史数据图表、趋势分析
- **告警管理**: 告警规则配置、告警历史查询
- **配置管理**: 在线配置修改、配置热更新
- **日志查看**: 实时日志查看、日志搜索过滤

访问管理控制台：`http://localhost:8842`

## 监控和维护

### 系统监控

服务器提供多维度的系统监控：

#### 连接监控
- 总连接数统计
- 活跃连接数监控
- 设备在线状态跟踪
- 连接成功率分析

#### 性能监控
- QPS（每秒查询数）统计
- 平均处理时间监控
- 最大处理时间跟踪
- 系统吞吐量分析

#### 错误监控
- CRC校验错误统计
- 协议解码错误监控
- 连接错误分析
- 错误率趋势跟踪

#### 资源监控
- JVM内存使用监控
- 线程池状态跟踪
- GC执行统计
- 系统资源使用分析

### 告警配置

#### 默认告警规则

系统预置了以下告警规则：

```yaml
# 错误率告警
error_rate_high:
  threshold: 5.0%
  level: WARNING
  description: "错误率过高"

# 连接数告警  
connection_count_high:
  threshold: 1000
  level: WARNING
  description: "连接数过高"

# 处理时间告警
processing_time_high:
  threshold: 1000ms
  level: WARNING
  description: "处理时间过长"

# 内存使用告警
memory_usage_high:
  threshold: 85%
  level: ERROR
  description: "内存使用率过高"
```

#### 自定义告警规则

可以通过配置文件或管理界面添加自定义告警规则：

```java
// 添加自定义告警规则
AlertManager alertManager = AlertManager.getInstance();
alertManager.addRule("custom_rule", new AlertRule() {
    @Override
    public boolean shouldTrigger(MetricsSnapshot snapshot) {
        return snapshot.getCurrentQPS() > 100;
    }
    
    @Override
    public AlertLevel getLevel() {
        return AlertLevel.WARNING;
    }
    
    @Override
    public String getMessage() {
        return "QPS超过100";
    }
});
```

### 性能优化

#### 自动优化

系统会自动执行以下优化操作：

- **线程池调优**: 根据负载自动调整线程池大小
- **内存管理**: 自动建议垃圾回收，防止内存泄漏
- **连接管理**: 自动清理空闲连接，优化连接池
- **缓存管理**: 自动清理过期缓存，优化内存使用

#### 手动优化

可以通过管理界面或API进行手动优化：

```bash
# 通过API触发优化
curl -X POST http://localhost:8842/api/optimize

# 强制垃圾回收
curl -X POST http://localhost:8842/api/gc

# 清理缓存
curl -X POST http://localhost:8842/api/cache/clear
```

#### JVM调优建议

推荐的JVM启动参数：

```bash
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+PrintGCDetails \
     -XX:+PrintGCTimeStamps \
     -Xloggc:gc.log \
     -jar gt06-protocol-server.jar
```

### 容错处理

#### 连接容错

- **自动重连**: 设备断线后自动尝试重连
- **连接超时**: 设置合理的连接超时时间
- **连接池管理**: 维护健康的连接池状态

#### 数据容错

- **CRC校验**: 确保数据传输完整性
- **数据重传**: 错误数据自动请求重传
- **格式验证**: 严格的数据格式验证

#### 系统容错

- **异常隔离**: 单个连接异常不影响其他连接
- **优雅降级**: 系统负载过高时自动降级
- **熔断保护**: 防止级联故障

## 故障排除

### 常见问题及解决方案

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8841

# 停止占用进程
taskkill /PID <进程ID> /F

# 或更换端口
java -jar gt06-protocol-server.jar 8842
```

#### 2. 内存不足
```bash
# 增加堆内存
java -Xmx4g -jar gt06-protocol-server.jar

# 监控内存使用
jstat -gc <进程ID> 1s
```

#### 3. 连接超时
- 检查网络连接状态
- 调整防火墙设置
- 增加连接超时时间

#### 4. CRC校验失败
- 检查数据传输完整性
- 验证设备端协议实现
- 查看详细错误日志

#### 5. 性能问题
```bash
# 查看线程状态
jstack <进程ID>

# 查看内存使用
jmap -histo <进程ID>

# 性能分析
jprofile <进程ID>
```

### 调试模式

#### 启用详细日志
```bash
java -Dlogging.level.com.gt06=DEBUG \
     -Dlogging.level.com.gt06.protocol=TRACE \
     -jar gt06-protocol-server.jar
```

#### 性能分析
```bash
# 启用JFR性能分析
java -XX:+FlightRecorder \
     -XX:StartFlightRecording=duration=60s,filename=gt06-profile.jfr \
     -jar gt06-protocol-server.jar
```

#### 内存分析
```bash
# 生成内存快照
jmap -dump:format=b,file=heap.hprof <进程ID>

# 使用MAT分析内存快照
```

### 监控指标说明

#### 关键性能指标(KPI)

| 指标名称 | 说明 | 正常范围 | 告警阈值 |
|---------|------|----------|----------|
| 连接成功率 | 设备连接成功的比例 | >95% | <90% |
| 平均响应时间 | 协议包处理平均时间 | <100ms | >500ms |
| QPS | 每秒处理的协议包数量 | 根据业务需求 | - |
| 错误率 | 处理失败的协议包比例 | <1% | >5% |
| 内存使用率 | JVM堆内存使用比例 | <70% | >85% |
| CPU使用率 | 系统CPU使用比例 | <60% | >80% |

#### 业务指标

| 指标名称 | 说明 | 监控意义 |
|---------|------|----------|
| 在线设备数 | 当前在线的设备数量 | 业务规模指标 |
| 定位数据量 | 接收的定位数据包数量 | 核心业务指标 |
| 心跳包频率 | 设备心跳包发送频率 | 设备健康度指标 |
| 报警触发数 | 设备报警的触发次数 | 安全监控指标 |

## 高级配置

### 配置文件详解

系统支持通过配置文件进行详细配置：

```properties
# 服务器配置
server.port=8841
server.thread.pool.size=50
server.connection.timeout=30000
server.max.connections=1000

# 监控配置
monitor.enabled=true
monitor.report.interval=30000
monitor.metrics.retention=7d

# 告警配置
alert.enabled=true
alert.error.rate.threshold=5.0
alert.connection.threshold=800
alert.processing.time.threshold=1000

# 性能优化配置
performance.auto.optimize=true
performance.gc.threshold=0.8
performance.thread.pool.auto.adjust=true

# 管理控制台配置
management.port=8842
management.enabled=true
```

### 集群部署

支持多实例集群部署：

```bash
# 节点1
java -Dserver.port=8841 -Dmanagement.port=8842 -jar gt06-server.jar

# 节点2  
java -Dserver.port=8843 -Dmanagement.port=8844 -jar gt06-server.jar

# 负载均衡配置
# 使用Nginx或HAProxy进行负载均衡
```

## API接口文档

### 监控API

#### 获取系统指标
```http
GET /api/metrics
```

响应示例：
```json
{
  "totalConnections": 150,
  "activeConnections": 120,
  "totalPacketsReceived": 50000,
  "currentQPS": 25.5,
  "averageProcessingTime": 45.2,
  "errorRate": 0.8,
  "uptime": 3600000
}
```

#### 获取告警信息
```http
GET /api/alerts
```

#### 获取配置信息
```http
GET /api/config
```

#### 健康检查
```http
GET /api/health
```

### 管理API

#### 触发优化
```http
POST /api/optimize
```

#### 强制垃圾回收
```http
POST /api/gc
```

#### 重载配置
```http
POST /api/config/reload
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 技术支持

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至技术支持团队
- 查看在线文档和FAQ

## 更新日志

### v2.0.0 (2024-02-28)
- 新增实时监控系统
- 新增智能告警功能
- 新增容错与恢复机制
- 新增性能优化引擎
- 新增Web管理控制台
- 优化协议解析性能
- 增强系统稳定性

### v1.0.0 (2024-02-01)
- 初始版本发布
- 基础GT06协议支持
- TCP服务器实现
- 协议包解析功能
