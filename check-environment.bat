@echo off
chcp 65001 >nul
echo ========================================
echo GT06协议服务器环境检查工具
echo ========================================
echo.

set ERROR_COUNT=0

echo [1/4] 检查Java版本...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置PATH
    set /a ERROR_COUNT+=1
    goto :check_maven
)

for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do (
    set JAVA_VERSION=%%i
    set JAVA_VERSION=!JAVA_VERSION:"=!
)

echo ✓ Java版本: %JAVA_VERSION%

echo %JAVA_VERSION% | findstr "^21\." >nul
if %errorlevel% equ 0 (
    echo ✅ JDK21检查通过
) else (
    echo ❌ 需要JDK21，当前版本: %JAVA_VERSION%
    set /a ERROR_COUNT+=1
)

:check_maven
echo.
echo [2/4] 检查Maven...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven未安装或未配置PATH
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Maven已安装
    for /f "tokens=3" %%i in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do (
        echo ✓ Maven版本: %%i
    )
)

echo.
echo [3/4] 检查项目文件...
if exist "pom.xml" (
    echo ✅ pom.xml存在
) else (
    echo ❌ pom.xml不存在
    set /a ERROR_COUNT+=1
)

if exist "src\main\java\com\gt06\Application.java" (
    echo ✅ 主类文件存在
) else (
    echo ❌ 主类文件不存在
    set /a ERROR_COUNT+=1
)

echo.
echo [4/4] 检查编译环境...
if %ERROR_COUNT% gtr 0 (
    echo ❌ 发现 %ERROR_COUNT% 个环境问题
    echo.
    echo 🔧 解决建议:
    if %JAVA_VERSION:~0,2% neq 21 (
        echo - 安装JDK21: https://jdk.java.net/21/
    )
    mvn -version >nul 2>&1
    if %errorlevel% neq 0 (
        echo - 安装Maven: https://maven.apache.org/download.cgi
    )
    echo - 参考: 环境配置指南.md
) else (
    echo ✅ 环境检查通过！
    echo.
    echo 🚀 可以开始编译项目:
    echo    mvn clean compile
    echo.
    echo 📖 更多信息请查看: 环境配置指南.md
)

echo.
echo ========================================
pause
