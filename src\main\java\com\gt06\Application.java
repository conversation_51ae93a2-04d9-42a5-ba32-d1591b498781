package com.gt06;

import com.gt06.server.GT06Server;
import com.gt06.monitor.ServerMetrics;
import com.gt06.alert.AlertManager;
import com.gt06.fault.FaultTolerance;
import com.gt06.performance.PerformanceOptimizer;
import com.gt06.management.ManagementConsole;
import com.gt06.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.StructuredTaskScope;
import java.util.concurrent.Future;
import java.time.Duration;

/**
 * GT06协议服务器启动类
 * 基于JDK21开发，集成监控、告警、容错、性能优化等功能
 * 
 * <AUTHOR>
 * @version 2.1.0 (JDK21版本)
 */
public class Application {
    
    private static final Logger logger = LoggerFactory.getLogger(Application.class);
    
    /** 服务器实例 */
    private static GT06Server server;
    
    /** 管理控制台 */
    private static ManagementConsole managementConsole;
    
    /** 各个管理组件 */
    private static ServerMetrics serverMetrics;
    private static AlertManager alertManager;
    private static FaultTolerance faultTolerance;
    private static PerformanceOptimizer performanceOptimizer;
    private static ConfigManager configManager;
    
    public static void main(String[] args) {
        try {
            // 打印启动横幅
            printBanner();
            
            // 初始化配置管理器
            configManager = ConfigManager.getInstance();
            logger.info("配置管理器初始化完成");
            
            // 获取服务器端口
            int port = getServerPort(args);
            
            // 使用结构化并发初始化组件
            initializeComponentsConcurrently();
            
            // 启动服务器
            startServer(port);
            
            // 使用结构化并发启动管理组件
            startManagementComponentsConcurrently();
            
            // 注册关闭钩子
            registerShutdownHook();
            
            logger.info("GT06协议服务器完全启动成功！");
            logger.info("服务器端口: {}", port);
            logger.info("管理控制台: http://localhost:{}", managementConsole.getManagementPort());
            logger.info("运行环境: JDK {}", System.getProperty("java.version"));
            
            // 保持主线程运行
            keepAlive();
            
        } catch (Exception e) {
            logger.error("启动GT06协议服务器失败", e);
            shutdown();
            System.exit(1);
        }
    }
    
    /**
     * 打印启动横幅 - 使用文本块特性
     */
    private static void printBanner() {
        String banner = """
                
                  ██████╗ ████████╗ ██████╗  ██████╗ 
                 ██╔════╝ ╚══██╔══╝██╔═████╗██╔════╝ 
                 ██║  ███╗   ██║   ██║██╔██║███████╗ 
                 ██║   ██║   ██║   ████╔╝██║██╔═══██╗
                 ╚██████╔╝   ██║   ╚██████╔╝╚██████╔╝
                  ╚═════╝    ╚═╝    ╚═════╝  ╚═════╝ 
                
                 GT06协议服务器 v2.1.0 (JDK21版本)
                 企业级物联网设备通信平台
                 集成监控、告警、容错、性能优化
                 支持虚拟线程和结构化并发
                
                """;
        System.out.println(banner);
    }
    
    /**
     * 获取服务器端口 - 使用switch表达式
     */
    private static int getServerPort(String[] args) {
        int defaultPort = configManager.getInt("server.port", 8841);
        
        return switch (args.length) {
            case 0 -> {
                logger.info("使用默认端口: {}", defaultPort);
                yield defaultPort;
            }
            case 1 -> {
                try {
                    int port = Integer.parseInt(args[0]);
                    logger.info("使用命令行指定端口: {}", port);
                    yield port;
                } catch (NumberFormatException e) {
                    logger.warn("无效的端口号参数: {}, 使用默认端口: {}", args[0], defaultPort);
                    yield defaultPort;
                }
            }
            default -> {
                logger.warn("参数过多，只使用第一个端口参数: {}", args[0]);
                try {
                    yield Integer.parseInt(args[0]);
                } catch (NumberFormatException e) {
                    logger.warn("无效的端口号参数: {}, 使用默认端口: {}", args[0], defaultPort);
                    yield defaultPort;
                }
            }
        };
    }
    
    /**
     * 使用结构化并发初始化各个组件
     */
    private static void initializeComponentsConcurrently() throws InterruptedException {
        logger.info("正在并发初始化系统组件...");
        
        try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
            // 并发初始化各个组件
            Future<ServerMetrics> metricsTask = scope.fork(() -> {
                serverMetrics = ServerMetrics.getInstance();
                logger.info("服务器指标监控初始化完成");
                return serverMetrics;
            });
            
            Future<AlertManager> alertTask = scope.fork(() -> {
                alertManager = AlertManager.getInstance();
                logger.info("告警管理器初始化完成");
                return alertManager;
            });
            
            Future<FaultTolerance> faultTask = scope.fork(() -> {
                faultTolerance = FaultTolerance.getInstance();
                logger.info("容错管理器初始化完成");
                return faultTolerance;
            });
            
            Future<PerformanceOptimizer> optimizerTask = scope.fork(() -> {
                performanceOptimizer = PerformanceOptimizer.getInstance();
                logger.info("性能优化器初始化完成");
                return performanceOptimizer;
            });
            
            Future<ManagementConsole> consoleTask = scope.fork(() -> {
                managementConsole = ManagementConsole.getInstance();
                logger.info("管理控制台初始化完成");
                return managementConsole;
            });
            
            // 等待所有任务完成
            scope.join();
            scope.throwIfFailed();
            
            // 获取结果
            serverMetrics = metricsTask.resultNow();
            alertManager = alertTask.resultNow();
            faultTolerance = faultTask.resultNow();
            performanceOptimizer = optimizerTask.resultNow();
            managementConsole = consoleTask.resultNow();
            
            logger.info("所有系统组件并发初始化完成");
        }
    }
    
    /**
     * 启动服务器 - 使用虚拟线程
     */
    private static void startServer(int port) throws Exception {
        logger.info("正在启动GT06协议服务器，端口: {}", port);
        
        // 创建服务器实例
        server = new GT06Server(port);
        
        // 在虚拟线程中启动服务器
        Thread.ofVirtual()
            .name("GT06-Server-Starter")
            .start(() -> {
                try {
                    server.start();
                    logger.info("GT06协议服务器启动成功，端口: {}", port);
                } catch (Exception e) {
                    logger.error("启动GT06协议服务器失败", e);
                    throw new RuntimeException(e);
                }
            })
            .join(); // 等待启动完成
    }
    
    /**
     * 使用结构化并发启动管理组件
     */
    private static void startManagementComponentsConcurrently() throws InterruptedException {
        logger.info("正在并发启动管理组件...");
        
        try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
            // 并发启动各个管理组件
            scope.fork(() -> {
                serverMetrics.start();
                logger.info("服务器指标监控已启动");
                return null;
            });
            
            scope.fork(() -> {
                alertManager.start();
                logger.info("告警管理器已启动");
                return null;
            });
            
            scope.fork(() -> {
                faultTolerance.start();
                logger.info("容错管理器已启动");
                return null;
            });
            
            scope.fork(() -> {
                performanceOptimizer.start();
                logger.info("性能优化器已启动");
                return null;
            });
            
            scope.fork(() -> {
                managementConsole.start();
                logger.info("管理控制台已启动，访问地址: http://localhost:{}", 
                    managementConsole.getManagementPort());
                return null;
            });
            
            // 等待所有任务完成
            scope.join();
            scope.throwIfFailed();
            
            logger.info("所有管理组件并发启动完成");
        }
    }
    
    /**
     * 注册关闭钩子 - 使用虚拟线程
     */
    private static void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(
            Thread.ofVirtual()
                .name("ShutdownHook")
                .unstarted(() -> {
                    logger.info("接收到关闭信号，正在优雅关闭系统...");
                    shutdown();
                    logger.info("系统已完全关闭");
                })
        );
    }
    
    /**
     * 优雅关闭系统 - 使用结构化并发
     */
    private static void shutdown() {
        try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
            // 并发关闭各个组件
            scope.fork(() -> {
                if (managementConsole != null) {
                    managementConsole.stop();
                    logger.info("管理控制台已停止");
                }
                return null;
            });
            
            scope.fork(() -> {
                if (performanceOptimizer != null) {
                    performanceOptimizer.stop();
                    logger.info("性能优化器已停止");
                }
                return null;
            });
            
            scope.fork(() -> {
                if (faultTolerance != null) {
                    faultTolerance.stop();
                    logger.info("容错管理器已停止");
                }
                return null;
            });
            
            scope.fork(() -> {
                if (alertManager != null) {
                    alertManager.stop();
                    logger.info("告警管理器已停止");
                }
                return null;
            });
            
            scope.fork(() -> {
                if (serverMetrics != null) {
                    serverMetrics.stop();
                    logger.info("服务器指标监控已停止");
                }
                return null;
            });
            
            scope.fork(() -> {
                if (server != null) {
                    server.stop();
                    logger.info("GT06协议服务器已停止");
                }
                return null;
            });
            
            // 等待所有关闭任务完成，最多等待30秒
            scope.join();
            
        } catch (InterruptedException e) {
            logger.error("关闭系统时被中断", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("关闭系统时发生异常", e);
        }
    }
    
    /**
     * 保持主线程运行 - 使用虚拟线程
     */
    private static void keepAlive() {
        try {
            // 创建虚拟线程来监控系统状态
            Thread monitorThread = Thread.ofVirtual()
                .name("SystemMonitor")
                .start(() -> {
                    while (!Thread.currentThread().isInterrupted()) {
                        try {
                            Thread.sleep(Duration.ofSeconds(30));
                            
                            // 检查各个组件的健康状态
                            checkSystemHealth();
                            
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        } catch (Exception e) {
                            logger.error("系统健康检查时发生异常", e);
                        }
                    }
                });
            
            // 主线程等待
            Thread.currentThread().join();
            
        } catch (InterruptedException e) {
            logger.info("主线程被中断，开始关闭系统");
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 检查系统健康状态 - 使用模式匹配和记录类
     */
    private static void checkSystemHealth() {
        try {
            // 检查服务器状态
            var serverStatus = switch (server != null ? server.isRunning() : false) {
                case true -> "运行中";
                case false -> {
                    logger.error("GT06服务器已停止运行！");
                    yield "已停止";
                }
            };
            
            // 检查管理组件状态
            var consoleStatus = switch (managementConsole != null ? managementConsole.isRunning() : false) {
                case true -> "运行中";
                case false -> {
                    logger.warn("管理控制台已停止运行");
                    yield "已停止";
                }
            };
            
            // 记录系统运行状态
            if (serverMetrics != null) {
                ServerMetrics.MetricsSnapshot snapshot = serverMetrics.getSnapshot();
                
                // 使用文本块格式化健康检查报告
                String healthReport = """
                    系统健康检查报告:
                    - 服务器状态: %s
                    - 管理控制台状态: %s
                    - 活跃连接数: %d
                    - 当前QPS: %.2f
                    - 错误率: %.2f%%
                    - 系统运行时间: %d 分钟
                    """.formatted(
                        serverStatus,
                        consoleStatus,
                        snapshot.getActiveConnections(),
                        snapshot.getCurrentQPS(),
                        snapshot.getErrorRate(),
                        snapshot.getUptime() / 60000
                    );
                
                logger.debug(healthReport);
            }
            
        } catch (Exception e) {
            logger.error("系统健康检查失败", e);
        }
    }
    
    // 使用记录类定义系统状态
    public record SystemStatus(
        String serverStatus,
        String consoleStatus,
        int activeConnections,
        double currentQPS,
        double errorRate,
        long uptime
    ) {}
    
    /**
     * 获取系统状态 - 返回记录类
     */
    public static SystemStatus getSystemStatus() {
        if (server == null || serverMetrics == null || managementConsole == null) {
            return new SystemStatus("未初始化", "未初始化", 0, 0.0, 0.0, 0);
        }
        
        ServerMetrics.MetricsSnapshot snapshot = serverMetrics.getSnapshot();
        
        return new SystemStatus(
            server.isRunning() ? "运行中" : "已停止",
            managementConsole.isRunning() ? "运行中" : "已停止",
            snapshot.getActiveConnections(),
            snapshot.getCurrentQPS(),
            snapshot.getErrorRate(),
            snapshot.getUptime()
        );
    }
    
    // Getter方法保持不变，用于外部访问
    public static GT06Server getServer() { return server; }
    public static ManagementConsole getManagementConsole() { return managementConsole; }
    public static ServerMetrics getServerMetrics() { return serverMetrics; }
    public static AlertManager getAlertManager() { return alertManager; }
    public static FaultTolerance getFaultTolerance() { return faultTolerance; }
    public static PerformanceOptimizer getPerformanceOptimizer() { return performanceOptimizer; }
}