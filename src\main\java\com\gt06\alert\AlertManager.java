package com.gt06.alert;

import com.gt06.monitor.ServerMetrics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 告警管理器
 * 监控系统状态并触发相应告警
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class AlertManager {
    
    private static final Logger logger = LoggerFactory.getLogger(AlertManager.class);
    
    /** 单例实例 */
    private static volatile AlertManager instance;
    
    /** 告警规则列表 */
    private final List<AlertRule> alertRules = new CopyOnWriteArrayList<>();
    
    /** 告警处理器列表 */
    private final List<AlertHandler> alertHandlers = new CopyOnWriteArrayList<>();
    
    /** 告警状态缓存 */
    private final Map<String, AlertStatus> alertStatusMap = new ConcurrentHashMap<>();
    
    /** 定时检查执行器 */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    /** 告警处理执行器 */
    private final ExecutorService alertExecutor = Executors.newFixedThreadPool(5);
    
    /** 运行状态 */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /** 服务器指标引用 */
    private final ServerMetrics serverMetrics;
    
    private AlertManager() {
        this.serverMetrics = ServerMetrics.getInstance();
        initializeDefaultRules();
        initializeDefaultHandlers();
    }
    
    /**
     * 获取单例实例
     */
    public static AlertManager getInstance() {
        if (instance == null) {
            synchronized (AlertManager.class) {
                if (instance == null) {
                    instance = new AlertManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动告警管理器
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            // 每30秒检查一次告警规则
            scheduler.scheduleAtFixedRate(this::checkAlerts, 30, 30, TimeUnit.SECONDS);
            logger.info("告警管理器已启动");
        }
    }
    
    /**
     * 停止告警管理器
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            scheduler.shutdown();
            alertExecutor.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                if (!alertExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    alertExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                alertExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            logger.info("告警管理器已停止");
        }
    }
    
    /**
     * 初始化默认告警规则
     */
    private void initializeDefaultRules() {
        // 高错误率告警
        addAlertRule(new AlertRule("HIGH_ERROR_RATE", "错误率过高", AlertLevel.CRITICAL) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                return snapshot.getErrorRate() > 5.0; // 错误率超过5%
            }
        });
        
        // 连接数过多告警
        addAlertRule(new AlertRule("HIGH_CONNECTION_COUNT", "连接数过多", AlertLevel.WARNING) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                return snapshot.getActiveConnections() > 1000; // 活跃连接超过1000
            }
        });
        
        // 处理时间过长告警
        addAlertRule(new AlertRule("HIGH_PROCESSING_TIME", "处理时间过长", AlertLevel.WARNING) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                return snapshot.getAverageProcessingTime() > 1000; // 平均处理时间超过1秒
            }
        });
        
        // QPS过高告警
        addAlertRule(new AlertRule("HIGH_QPS", "QPS过高", AlertLevel.WARNING) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                return snapshot.getCurrentQPS() > 10000; // QPS超过10000
            }
        });
        
        // CRC错误频繁告警
        addAlertRule(new AlertRule("FREQUENT_CRC_ERRORS", "CRC错误频繁", AlertLevel.MAJOR) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                long totalPackets = snapshot.getTotalPacketsReceived();
                long crcErrors = snapshot.getCrcErrors();
                if (totalPackets == 0) return false;
                return (double) crcErrors / totalPackets > 0.01; // CRC错误率超过1%
            }
        });
        
        // 内存使用率告警
        addAlertRule(new AlertRule("HIGH_MEMORY_USAGE", "内存使用率过高", AlertLevel.CRITICAL) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                Runtime runtime = Runtime.getRuntime();
                long maxMemory = runtime.maxMemory();
                long totalMemory = runtime.totalMemory();
                long freeMemory = runtime.freeMemory();
                long usedMemory = totalMemory - freeMemory;
                double memoryUsage = (double) usedMemory / maxMemory;
                return memoryUsage > 0.85; // 内存使用率超过85%
            }
        });
        
        // 设备离线告警
        addAlertRule(new AlertRule("DEVICE_OFFLINE", "设备大量离线", AlertLevel.MAJOR) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                // 如果在线设备数突然下降超过50%，触发告警
                return snapshot.getOnlineDevices() < 10 && snapshot.getTotalConnections() > 100;
            }
        });
    }
    
    /**
     * 初始化默认告警处理器
     */
    private void initializeDefaultHandlers() {
        // 日志告警处理器
        addAlertHandler(new LogAlertHandler());
        
        // 邮件告警处理器（示例）
        // addAlertHandler(new EmailAlertHandler());
        
        // 短信告警处理器（示例）
        // addAlertHandler(new SmsAlertHandler());
    }
    
    /**
     * 添加告警规则
     */
    public void addAlertRule(AlertRule rule) {
        alertRules.add(rule);
        logger.info("添加告警规则: {} - {}", rule.getName(), rule.getDescription());
    }
    
    /**
     * 添加告警处理器
     */
    public void addAlertHandler(AlertHandler handler) {
        alertHandlers.add(handler);
        logger.info("添加告警处理器: {}", handler.getClass().getSimpleName());
    }
    
    /**
     * 检查所有告警规则
     */
    private void checkAlerts() {
        try {
            ServerMetrics.MetricsSnapshot snapshot = serverMetrics.getSnapshot();
            
            for (AlertRule rule : alertRules) {
                checkSingleAlert(rule, snapshot);
            }
            
        } catch (Exception e) {
            logger.error("检查告警规则时发生异常", e);
        }
    }
    
    /**
     * 检查单个告警规则
     */
    private void checkSingleAlert(AlertRule rule, ServerMetrics.MetricsSnapshot snapshot) {
        try {
            boolean shouldAlert = rule.evaluate(snapshot);
            String ruleId = rule.getId();
            AlertStatus currentStatus = alertStatusMap.get(ruleId);
            
            if (shouldAlert) {
                if (currentStatus == null || currentStatus.getStatus() != AlertStatus.Status.ACTIVE) {
                    // 触发新告警
                    AlertStatus newStatus = new AlertStatus(ruleId, AlertStatus.Status.ACTIVE, 
                                                          System.currentTimeMillis(), rule.getLevel());
                    alertStatusMap.put(ruleId, newStatus);
                    
                    Alert alert = new Alert(rule, snapshot, System.currentTimeMillis());
                    triggerAlert(alert);
                } else {
                    // 更新现有告警的最后检查时间
                    currentStatus.updateLastCheck();
                }
            } else {
                if (currentStatus != null && currentStatus.getStatus() == AlertStatus.Status.ACTIVE) {
                    // 告警恢复
                    currentStatus.setStatus(AlertStatus.Status.RESOLVED);
                    currentStatus.setResolvedTime(System.currentTimeMillis());
                    
                    Alert recoveryAlert = new Alert(rule, snapshot, System.currentTimeMillis(), true);
                    triggerAlert(recoveryAlert);
                }
            }
            
        } catch (Exception e) {
            logger.error("检查告警规则 {} 时发生异常", rule.getId(), e);
        }
    }
    
    /**
     * 触发告警
     */
    private void triggerAlert(Alert alert) {
        alertExecutor.submit(() -> {
            for (AlertHandler handler : alertHandlers) {
                try {
                    handler.handleAlert(alert);
                } catch (Exception e) {
                    logger.error("处理告警时发生异常: {}", handler.getClass().getSimpleName(), e);
                }
            }
        });
    }
    
    /**
     * 手动触发告警
     */
    public void manualAlert(String message, AlertLevel level) {
        AlertRule manualRule = new AlertRule("MANUAL_ALERT", message, level) {
            @Override
            public boolean evaluate(ServerMetrics.MetricsSnapshot snapshot) {
                return true;
            }
        };
        
        Alert alert = new Alert(manualRule, serverMetrics.getSnapshot(), System.currentTimeMillis());
        triggerAlert(alert);
    }
    
    /**
     * 获取当前告警状态
     */
    public Map<String, AlertStatus> getCurrentAlertStatus() {
        return new ConcurrentHashMap<>(alertStatusMap);
    }
    
    /**
     * 获取活跃告警数量
     */
    public long getActiveAlertCount() {
        return alertStatusMap.values().stream()
                .filter(status -> status.getStatus() == AlertStatus.Status.ACTIVE)
                .count();
    }
    
    /**
     * 清理已解决的告警状态
     */
    public void cleanupResolvedAlerts() {
        long now = System.currentTimeMillis();
        alertStatusMap.entrySet().removeIf(entry -> {
            AlertStatus status = entry.getValue();
            return status.getStatus() == AlertStatus.Status.RESOLVED && 
                   now - status.getResolvedTime() > TimeUnit.HOURS.toMillis(24);
        });
    }
    
    /**
     * 告警规则抽象类
     */
    public abstract static class AlertRule {
        private final String id;
        private final String name;
        private final String description;
        private final AlertLevel level;
        
        public AlertRule(String id, String description, AlertLevel level) {
            this.id = id;
            this.name = id;
            this.description = description;
            this.level = level;
        }
        
        public abstract boolean evaluate(ServerMetrics.MetricsSnapshot snapshot);
        
        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public AlertLevel getLevel() { return level; }
    }
    
    /**
     * 告警处理器接口
     */
    public interface AlertHandler {
        void handleAlert(Alert alert);
    }
    
    /**
     * 日志告警处理器
     */
    public static class LogAlertHandler implements AlertHandler {
        private static final Logger alertLogger = LoggerFactory.getLogger("ALERT");
        
        @Override
        public void handleAlert(Alert alert) {
            if (alert.isRecovery()) {
                alertLogger.info("【告警恢复】{} - {} (级别: {})", 
                               alert.getRule().getName(),
                               alert.getRule().getDescription(),
                               alert.getRule().getLevel());
            } else {
                alertLogger.warn("【告警触发】{} - {} (级别: {})", 
                               alert.getRule().getName(),
                               alert.getRule().getDescription(),
                               alert.getRule().getLevel());
            }
        }
    }
    
    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        INFO("信息"),
        WARNING("警告"),
        MAJOR("重要"),
        CRITICAL("严重");
        
        private final String description;
        
        AlertLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 告警对象
     */
    public static class Alert {
        private final AlertRule rule;
        private final ServerMetrics.MetricsSnapshot snapshot;
        private final long timestamp;
        private final boolean recovery;
        
        public Alert(AlertRule rule, ServerMetrics.MetricsSnapshot snapshot, long timestamp) {
            this(rule, snapshot, timestamp, false);
        }
        
        public Alert(AlertRule rule, ServerMetrics.MetricsSnapshot snapshot, long timestamp, boolean recovery) {
            this.rule = rule;
            this.snapshot = snapshot;
            this.timestamp = timestamp;
            this.recovery = recovery;
        }
        
        public AlertRule getRule() { return rule; }
        public ServerMetrics.MetricsSnapshot getSnapshot() { return snapshot; }
        public long getTimestamp() { return timestamp; }
        public boolean isRecovery() { return recovery; }
    }
    
    /**
     * 告警状态
     */
    public static class AlertStatus {
        private final String ruleId;
        private Status status;
        private final long triggeredTime;
        private long lastCheckTime;
        private long resolvedTime;
        private final AlertLevel level;
        
        public AlertStatus(String ruleId, Status status, long triggeredTime, AlertLevel level) {
            this.ruleId = ruleId;
            this.status = status;
            this.triggeredTime = triggeredTime;
            this.lastCheckTime = triggeredTime;
            this.level = level;
        }
        
        public void updateLastCheck() {
            this.lastCheckTime = System.currentTimeMillis();
        }
        
        public void setStatus(Status status) { this.status = status; }
        public void setResolvedTime(long resolvedTime) { this.resolvedTime = resolvedTime; }
        
        public String getRuleId() { return ruleId; }
        public Status getStatus() { return status; }
        public long getTriggeredTime() { return triggeredTime; }
        public long getLastCheckTime() { return lastCheckTime; }
        public long getResolvedTime() { return resolvedTime; }
        public AlertLevel getLevel() { return level; }
        
        public enum Status {
            ACTIVE, RESOLVED
        }
    }
}