package com.gt06.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.List;
import java.util.ArrayList;
import java.nio.file.*;

/**
 * 配置管理器
 * 支持动态配置加载、热更新和配置监听
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    
    /** 单例实例 */
    private static volatile ConfigManager instance;
    
    /** 配置文件路径 */
    private static final String CONFIG_FILE = "gt06-server.properties";
    
    /** 配置属性 */
    private final Properties properties = new Properties();
    
    /** 配置监听器列表 */
    private final List<ConfigChangeListener> listeners = new ArrayList<>();
    
    /** 文件监控服务 */
    private WatchService watchService;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    /** 配置缓存 */
    private final ConcurrentHashMap<String, Object> configCache = new ConcurrentHashMap<>();
    
    /** 运行状态 */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /** 配置文件最后修改时间 */
    private long lastModified = 0;
    
    private ConfigManager() {
        loadConfiguration();
        initializeFileWatcher();
    }
    
    /**
     * 获取单例实例
     */
    public static ConfigManager getInstance() {
        if (instance == null) {
            synchronized (ConfigManager.class) {
                if (instance == null) {
                    instance = new ConfigManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动配置管理器
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            // 定期检查配置文件变化
            scheduler.scheduleAtFixedRate(this::checkConfigChanges, 30, 30, TimeUnit.SECONDS);
            logger.info("配置管理器已启动");
        }
    }
    
    /**
     * 停止配置管理器
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            scheduler.shutdown();
            if (watchService != null) {
                try {
                    watchService.close();
                } catch (IOException e) {
                    logger.error("关闭文件监控服务失败", e);
                }
            }
            logger.info("配置管理器已停止");
        }
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfiguration() {
        try {
            // 首先尝试从类路径加载
            InputStream is = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE);
            if (is == null) {
                // 尝试从当前目录加载
                File configFile = new File(CONFIG_FILE);
                if (configFile.exists()) {
                    is = new FileInputStream(configFile);
                    lastModified = configFile.lastModified();
                }
            }
            
            if (is != null) {
                properties.load(is);
                is.close();
                logger.info("配置文件加载成功: {}", CONFIG_FILE);
                
                // 清空缓存，强制重新计算
                configCache.clear();
                
                // 通知监听器
                notifyConfigChanged();
            } else {
                logger.warn("配置文件不存在，使用默认配置: {}", CONFIG_FILE);
                loadDefaultConfiguration();
            }
            
        } catch (IOException e) {
            logger.error("加载配置文件失败: {}", CONFIG_FILE, e);
            loadDefaultConfiguration();
        }
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfiguration() {
        properties.setProperty("server.port", "8841");
        properties.setProperty("server.thread.pool.size", "50");
        properties.setProperty("server.connection.timeout", "300000");
        properties.setProperty("server.max.connections", "1000");
        
        // 监控配置
        properties.setProperty("monitor.enabled", "true");
        properties.setProperty("monitor.report.interval", "60");
        
        // 告警配置
        properties.setProperty("alert.enabled", "true");
        properties.setProperty("alert.error.rate.threshold", "5.0");
        properties.setProperty("alert.connection.threshold", "1000");
        properties.setProperty("alert.processing.time.threshold", "1000");
        
        // 容错配置
        properties.setProperty("fault.tolerance.enabled", "true");
        properties.setProperty("circuit.breaker.failure.threshold", "5");
        properties.setProperty("circuit.breaker.timeout", "60000");
        properties.setProperty("retry.max.attempts", "3");
        properties.setProperty("retry.initial.delay", "1000");
        
        // 日志配置
        properties.setProperty("log.level", "INFO");
        properties.setProperty("log.file.path", "logs/gt06-server.log");
        properties.setProperty("log.max.file.size", "100MB");
        properties.setProperty("log.max.history", "30");
        
        logger.info("已加载默认配置");
    }
    
    /**
     * 初始化文件监控
     */
    private void initializeFileWatcher() {
        try {
            watchService = FileSystems.getDefault().newWatchService();
            Path configPath = Paths.get(".");
            configPath.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);
            
            // 启动文件监控线程
            Thread watchThread = new Thread(this::watchConfigFile);
            watchThread.setDaemon(true);
            watchThread.setName("ConfigFileWatcher");
            watchThread.start();
            
        } catch (IOException e) {
            logger.error("初始化文件监控失败", e);
        }
    }
    
    /**
     * 监控配置文件变化
     */
    private void watchConfigFile() {
        while (running.get()) {
            try {
                WatchKey key = watchService.take();
                for (WatchEvent<?> event : key.pollEvents()) {
                    if (event.context().toString().equals(CONFIG_FILE)) {
                        logger.info("检测到配置文件变化，重新加载配置");
                        Thread.sleep(1000); // 等待文件写入完成
                        loadConfiguration();
                    }
                }
                key.reset();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("监控配置文件时发生异常", e);
            }
        }
    }
    
    /**
     * 检查配置文件变化
     */
    private void checkConfigChanges() {
        File configFile = new File(CONFIG_FILE);
        if (configFile.exists() && configFile.lastModified() > lastModified) {
            logger.info("检测到配置文件时间戳变化，重新加载配置");
            loadConfiguration();
        }
    }
    
    /**
     * 获取字符串配置
     */
    public String getString(String key) {
        return getString(key, null);
    }
    
    /**
     * 获取字符串配置（带默认值）
     */
    public String getString(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取整数配置
     */
    public int getInt(String key) {
        return getInt(key, 0);
    }
    
    /**
     * 获取整数配置（带默认值）
     */
    public int getInt(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            logger.warn("配置项 {} 的值 {} 不是有效整数，使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取长整数配置
     */
    public long getLong(String key, long defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Long.parseLong(value.trim());
        } catch (NumberFormatException e) {
            logger.warn("配置项 {} 的值 {} 不是有效长整数，使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取双精度配置
     */
    public double getDouble(String key, double defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            logger.warn("配置项 {} 的值 {} 不是有效双精度数，使用默认值 {}", key, value, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔配置
     */
    public boolean getBoolean(String key) {
        return getBoolean(key, false);
    }
    
    /**
     * 获取布尔配置（带默认值）
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value.trim());
    }
    
    /**
     * 设置配置项
     */
    public void setProperty(String key, String value) {
        String oldValue = properties.getProperty(key);
        properties.setProperty(key, value);
        
        // 清除缓存
        configCache.remove(key);
        
        // 通知监听器
        notifyPropertyChanged(key, oldValue, value);
    }
    
    /**
     * 保存配置到文件
     */
    public void saveConfiguration() {
        try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE)) {
            properties.store(fos, "GT06服务器配置文件 - 自动生成");
            logger.info("配置已保存到文件: {}", CONFIG_FILE);
        } catch (IOException e) {
            logger.error("保存配置文件失败: {}", CONFIG_FILE, e);
        }
    }
    
    /**
     * 添加配置变化监听器
     */
    public void addConfigChangeListener(ConfigChangeListener listener) {
        synchronized (listeners) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除配置变化监听器
     */
    public void removeConfigChangeListener(ConfigChangeListener listener) {
        synchronized (listeners) {
            listeners.remove(listener);
        }
    }
    
    /**
     * 通知配置变化
     */
    private void notifyConfigChanged() {
        synchronized (listeners) {
            for (ConfigChangeListener listener : listeners) {
                try {
                    listener.onConfigChanged();
                } catch (Exception e) {
                    logger.error("通知配置变化监听器时发生异常", e);
                }
            }
        }
    }
    
    /**
     * 通知单个属性变化
     */
    private void notifyPropertyChanged(String key, String oldValue, String newValue) {
        synchronized (listeners) {
            for (ConfigChangeListener listener : listeners) {
                try {
                    listener.onPropertyChanged(key, oldValue, newValue);
                } catch (Exception e) {
                    logger.error("通知属性变化监听器时发生异常", e);
                }
            }
        }
    }
    
    /**
     * 获取所有配置项
     */
    public Properties getAllProperties() {
        return new Properties(properties);
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfiguration() {
        logger.info("手动重新加载配置");
        loadConfiguration();
    }
    
    /**
     * 获取服务器配置
     */
    public ServerConfig getServerConfig() {
        return (ServerConfig) configCache.computeIfAbsent("server.config", k -> {
            return new ServerConfig(
                getInt("server.port", 8841),
                getInt("server.thread.pool.size", 50),
                getLong("server.connection.timeout", 300000),
                getInt("server.max.connections", 1000)
            );
        });
    }
    
    /**
     * 获取监控配置
     */
    public MonitorConfig getMonitorConfig() {
        return (MonitorConfig) configCache.computeIfAbsent("monitor.config", k -> {
            return new MonitorConfig(
                getBoolean("monitor.enabled", true),
                getInt("monitor.report.interval", 60)
            );
        });
    }
    
    /**
     * 获取告警配置
     */
    public AlertConfig getAlertConfig() {
        return (AlertConfig) configCache.computeIfAbsent("alert.config", k -> {
            return new AlertConfig(
                getBoolean("alert.enabled", true),
                getDouble("alert.error.rate.threshold", 5.0),
                getInt("alert.connection.threshold", 1000),
                getLong("alert.processing.time.threshold", 1000)
            );
        });
    }
    
    /**
     * 配置变化监听器接口
     */
    public interface ConfigChangeListener {
        /**
         * 配置文件变化时调用
         */
        void onConfigChanged();
        
        /**
         * 单个属性变化时调用
         */
        default void onPropertyChanged(String key, String oldValue, String newValue) {
            // 默认实现为空
        }
    }
    
    /**
     * 服务器配置类
     */
    public static class ServerConfig {
        private final int port;
        private final int threadPoolSize;
        private final long connectionTimeout;
        private final int maxConnections;
        
        public ServerConfig(int port, int threadPoolSize, long connectionTimeout, int maxConnections) {
            this.port = port;
            this.threadPoolSize = threadPoolSize;
            this.connectionTimeout = connectionTimeout;
            this.maxConnections = maxConnections;
        }
        
        public int getPort() { return port; }
        public int getThreadPoolSize() { return threadPoolSize; }
        public long getConnectionTimeout() { return connectionTimeout; }
        public int getMaxConnections() { return maxConnections; }
    }
    
    /**
     * 监控配置类
     */
    public static class MonitorConfig {
        private final boolean enabled;
        private final int reportInterval;
        
        public MonitorConfig(boolean enabled, int reportInterval) {
            this.enabled = enabled;
            this.reportInterval = reportInterval;
        }
        
        public boolean isEnabled() { return enabled; }
        public int getReportInterval() { return reportInterval; }
    }
    
    /**
     * 告警配置类
     */
    public static class AlertConfig {
        private final boolean enabled;
        private final double errorRateThreshold;
        private final int connectionThreshold;
        private final long processingTimeThreshold;
        
        public AlertConfig(boolean enabled, double errorRateThreshold, 
                          int connectionThreshold, long processingTimeThreshold) {
            this.enabled = enabled;
            this.errorRateThreshold = errorRateThreshold;
            this.connectionThreshold = connectionThreshold;
            this.processingTimeThreshold = processingTimeThreshold;
        }
        
        public boolean isEnabled() { return enabled; }
        public double getErrorRateThreshold() { return errorRateThreshold; }
        public int getConnectionThreshold() { return connectionThreshold; }
        public long getProcessingTimeThreshold() { return processingTimeThreshold; }
    }
}