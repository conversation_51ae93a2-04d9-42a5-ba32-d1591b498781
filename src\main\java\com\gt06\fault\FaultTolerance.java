package com.gt06.fault;

import com.gt06.monitor.ServerMetrics;
import com.gt06.alert.AlertManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 容错处理系统
 * 提供系统级别的容错、恢复和自愈能力
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class FaultTolerance {
    
    private static final Logger logger = LoggerFactory.getLogger(FaultTolerance.class);
    
    /** 单例实例 */
    private static volatile FaultTolerance instance;
    
    /** 断路器映射 */
    private final Map<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    
    /** 重试策略映射 */
    private final Map<String, RetryPolicy> retryPolicies = new ConcurrentHashMap<>();
    
    /** 连接池管理 */
    private final ConnectionPoolManager connectionPoolManager;
    
    /** 故障检测器 */
    private final FailureDetector failureDetector;
    
    /** 自动恢复管理器 */
    private final AutoRecoveryManager autoRecoveryManager;
    
    /** 限流器 */
    private final RateLimiter rateLimiter;
    
    /** 定时任务执行器 */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);
    
    /** 运行状态 */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    private FaultTolerance() {
        this.connectionPoolManager = new ConnectionPoolManager();
        this.failureDetector = new FailureDetector();
        this.autoRecoveryManager = new AutoRecoveryManager();
        this.rateLimiter = new RateLimiter();
        
        initializeDefaultPolicies();
    }
    
    /**
     * 获取单例实例
     */
    public static FaultTolerance getInstance() {
        if (instance == null) {
            synchronized (FaultTolerance.class) {
                if (instance == null) {
                    instance = new FaultTolerance();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动容错系统
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            failureDetector.start();
            autoRecoveryManager.start();
            
            // 定期检查系统健康状态
            scheduler.scheduleAtFixedRate(this::performHealthCheck, 30, 30, TimeUnit.SECONDS);
            
            logger.info("容错系统已启动");
        }
    }
    
    /**
     * 停止容错系统
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            failureDetector.stop();
            autoRecoveryManager.stop();
            
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            logger.info("容错系统已停止");
        }
    }
    
    /**
     * 初始化默认策略
     */
    private void initializeDefaultPolicies() {
        // 数据库连接断路器
        addCircuitBreaker("database", new CircuitBreaker("database", 5, 60000, 30000));
        
        // 外部API断路器
        addCircuitBreaker("external_api", new CircuitBreaker("external_api", 3, 30000, 15000));
        
        // 默认重试策略
        addRetryPolicy("default", new RetryPolicy("default", 3, 1000, 2.0));
        addRetryPolicy("database", new RetryPolicy("database", 5, 500, 1.5));
    }
    
    /**
     * 添加断路器
     */
    public void addCircuitBreaker(String name, CircuitBreaker circuitBreaker) {
        circuitBreakers.put(name, circuitBreaker);
        logger.info("添加断路器: {}", name);
    }
    
    /**
     * 添加重试策略
     */
    public void addRetryPolicy(String name, RetryPolicy retryPolicy) {
        retryPolicies.put(name, retryPolicy);
        logger.info("添加重试策略: {}", name);
    }
    
    /**
     * 获取断路器
     */
    public CircuitBreaker getCircuitBreaker(String name) {
        return circuitBreakers.get(name);
    }
    
    /**
     * 获取重试策略
     */
    public RetryPolicy getRetryPolicy(String name) {
        return retryPolicies.get(name);
    }
    
    /**
     * 执行带容错的操作
     */
    public <T> T executeWithFaultTolerance(String operationName, Callable<T> operation) throws Exception {
        return executeWithFaultTolerance(operationName, operation, "default");
    }
    
    /**
     * 执行带容错的操作（指定策略）
     */
    public <T> T executeWithFaultTolerance(String operationName, Callable<T> operation, String policyName) throws Exception {
        CircuitBreaker circuitBreaker = circuitBreakers.get(operationName);
        RetryPolicy retryPolicy = retryPolicies.get(policyName);
        
        if (circuitBreaker != null && circuitBreaker.getState() == CircuitBreaker.State.OPEN) {
            throw new CircuitBreakerOpenException("断路器开启: " + operationName);
        }
        
        Exception lastException = null;
        int attempts = retryPolicy != null ? retryPolicy.getMaxAttempts() : 1;
        long delay = retryPolicy != null ? retryPolicy.getInitialDelay() : 0;
        
        for (int i = 0; i < attempts; i++) {
            try {
                if (i > 0 && delay > 0) {
                    Thread.sleep(delay);
                    if (retryPolicy != null) {
                        delay = (long) (delay * retryPolicy.getBackoffMultiplier());
                    }
                }
                
                T result = operation.call();
                
                // 操作成功，记录成功
                if (circuitBreaker != null) {
                    circuitBreaker.recordSuccess();
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                
                // 记录失败
                if (circuitBreaker != null) {
                    circuitBreaker.recordFailure();
                }
                
                logger.warn("操作 {} 第 {} 次尝试失败: {}", operationName, i + 1, e.getMessage());
                
                // 如果是最后一次尝试，不再重试
                if (i == attempts - 1) {
                    break;
                }
            }
        }
        
        throw new OperationFailedException("操作失败: " + operationName, lastException);
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            // 检查内存使用情况
            checkMemoryUsage();
            
            // 检查线程池状态
            checkThreadPoolStatus();
            
            // 检查断路器状态
            checkCircuitBreakerStatus();
            
            // 检查连接池状态
            connectionPoolManager.performHealthCheck();
            
        } catch (Exception e) {
            logger.error("健康检查时发生异常", e);
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsage = (double) usedMemory / maxMemory;
        
        if (memoryUsage > 0.9) {
            logger.warn("内存使用率过高: {:.2f}%", memoryUsage * 100);
            AlertManager.getInstance().manualAlert("内存使用率过高: " + String.format("%.2f%%", memoryUsage * 100), 
                                                 AlertManager.AlertLevel.CRITICAL);
            
            // 触发垃圾回收
            System.gc();
        }
    }
    
    /**
     * 检查线程池状态
     */
    private void checkThreadPoolStatus() {
        // 这里可以检查各种线程池的状态
        // 例如：活跃线程数、队列长度等
    }
    
    /**
     * 检查断路器状态
     */
    private void checkCircuitBreakerStatus() {
        for (Map.Entry<String, CircuitBreaker> entry : circuitBreakers.entrySet()) {
            CircuitBreaker cb = entry.getValue();
            if (cb.getState() == CircuitBreaker.State.OPEN) {
                logger.warn("断路器 {} 处于开启状态", entry.getKey());
            }
        }
    }
    
    /**
     * 获取连接池管理器
     */
    public ConnectionPoolManager getConnectionPoolManager() {
        return connectionPoolManager;
    }
    
    /**
     * 获取限流器
     */
    public RateLimiter getRateLimiter() {
        return rateLimiter;
    }
    
    /**
     * 断路器类
     */
    public static class CircuitBreaker {
        private final String name;
        private final int failureThreshold;
        private final long timeout;
        private final long retryTimeout;
        
        private final AtomicInteger failureCount = new AtomicInteger(0);
        private final AtomicLong lastFailureTime = new AtomicLong(0);
        private volatile State state = State.CLOSED;
        
        public CircuitBreaker(String name, int failureThreshold, long timeout, long retryTimeout) {
            this.name = name;
            this.failureThreshold = failureThreshold;
            this.timeout = timeout;
            this.retryTimeout = retryTimeout;
        }
        
        public void recordSuccess() {
            failureCount.set(0);
            state = State.CLOSED;
        }
        
        public void recordFailure() {
            int failures = failureCount.incrementAndGet();
            lastFailureTime.set(System.currentTimeMillis());
            
            if (failures >= failureThreshold) {
                state = State.OPEN;
                logger.warn("断路器 {} 开启，失败次数: {}", name, failures);
            }
        }
        
        public State getState() {
            if (state == State.OPEN) {
                long now = System.currentTimeMillis();
                if (now - lastFailureTime.get() > retryTimeout) {
                    state = State.HALF_OPEN;
                    logger.info("断路器 {} 进入半开状态", name);
                }
            }
            return state;
        }
        
        public String getName() { return name; }
        public int getFailureCount() { return failureCount.get(); }
        
        public enum State {
            CLOSED, OPEN, HALF_OPEN
        }
    }
    
    /**
     * 重试策略类
     */
    public static class RetryPolicy {
        private final String name;
        private final int maxAttempts;
        private final long initialDelay;
        private final double backoffMultiplier;
        
        public RetryPolicy(String name, int maxAttempts, long initialDelay, double backoffMultiplier) {
            this.name = name;
            this.maxAttempts = maxAttempts;
            this.initialDelay = initialDelay;
            this.backoffMultiplier = backoffMultiplier;
        }
        
        public String getName() { return name; }
        public int getMaxAttempts() { return maxAttempts; }
        public long getInitialDelay() { return initialDelay; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
    }
    
    /**
     * 连接池管理器
     */
    public static class ConnectionPoolManager {
        private final Map<String, ConnectionPool> pools = new ConcurrentHashMap<>();
        
        public void addPool(String name, ConnectionPool pool) {
            pools.put(name, pool);
        }
        
        public ConnectionPool getPool(String name) {
            return pools.get(name);
        }
        
        public void performHealthCheck() {
            for (Map.Entry<String, ConnectionPool> entry : pools.entrySet()) {
                ConnectionPool pool = entry.getValue();
                if (pool.getActiveConnections() > pool.getMaxConnections() * 0.8) {
                    logger.warn("连接池 {} 使用率过高: {}/{}", 
                              entry.getKey(), pool.getActiveConnections(), pool.getMaxConnections());
                }
            }
        }
        
        public static class ConnectionPool {
            private final String name;
            private final int maxConnections;
            private final AtomicInteger activeConnections = new AtomicInteger(0);
            
            public ConnectionPool(String name, int maxConnections) {
                this.name = name;
                this.maxConnections = maxConnections;
            }
            
            public String getName() { return name; }
            public int getMaxConnections() { return maxConnections; }
            public int getActiveConnections() { return activeConnections.get(); }
            
            public void incrementActive() { activeConnections.incrementAndGet(); }
            public void decrementActive() { activeConnections.decrementAndGet(); }
        }
    }
    
    /**
     * 故障检测器
     */
    public static class FailureDetector {
        private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        private final AtomicBoolean running = new AtomicBoolean(false);
        
        public void start() {
            if (running.compareAndSet(false, true)) {
                scheduler.scheduleAtFixedRate(this::detectFailures, 10, 10, TimeUnit.SECONDS);
            }
        }
        
        public void stop() {
            if (running.compareAndSet(true, false)) {
                scheduler.shutdown();
            }
        }
        
        private void detectFailures() {
            // 检测各种故障模式
            // 例如：响应时间异常、错误率突增等
        }
    }
    
    /**
     * 自动恢复管理器
     */
    public static class AutoRecoveryManager {
        private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        private final AtomicBoolean running = new AtomicBoolean(false);
        
        public void start() {
            if (running.compareAndSet(false, true)) {
                scheduler.scheduleAtFixedRate(this::performRecovery, 60, 60, TimeUnit.SECONDS);
            }
        }
        
        public void stop() {
            if (running.compareAndSet(true, false)) {
                scheduler.shutdown();
            }
        }
        
        private void performRecovery() {
            // 执行自动恢复操作
            // 例如：重启失败的服务、清理资源等
        }
    }
    
    /**
     * 限流器
     */
    public static class RateLimiter {
        private final Map<String, TokenBucket> buckets = new ConcurrentHashMap<>();
        
        public void addLimit(String key, int capacity, int refillRate) {
            buckets.put(key, new TokenBucket(capacity, refillRate));
        }
        
        public boolean tryAcquire(String key) {
            TokenBucket bucket = buckets.get(key);
            return bucket != null && bucket.tryConsume();
        }
        
        private static class TokenBucket {
            private final int capacity;
            private final int refillRate;
            private final AtomicInteger tokens;
            private final AtomicLong lastRefillTime;
            
            public TokenBucket(int capacity, int refillRate) {
                this.capacity = capacity;
                this.refillRate = refillRate;
                this.tokens = new AtomicInteger(capacity);
                this.lastRefillTime = new AtomicLong(System.currentTimeMillis());
            }
            
            public boolean tryConsume() {
                refill();
                return tokens.getAndDecrement() > 0;
            }
            
            private void refill() {
                long now = System.currentTimeMillis();
                long lastRefill = lastRefillTime.get();
                long timePassed = now - lastRefill;
                
                if (timePassed > 1000) { // 每秒补充
                    int tokensToAdd = (int) (timePassed / 1000 * refillRate);
                    int currentTokens = tokens.get();
                    int newTokens = Math.min(capacity, currentTokens + tokensToAdd);
                    
                    if (tokens.compareAndSet(currentTokens, newTokens)) {
                        lastRefillTime.set(now);
                    }
                }
            }
        }
    }
    
    /**
     * 断路器开启异常
     */
    public static class CircuitBreakerOpenException extends Exception {
        public CircuitBreakerOpenException(String message) {
            super(message);
        }
    }
    
    /**
     * 操作失败异常
     */
    public static class OperationFailedException extends Exception {
        public OperationFailedException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}