package com.gt06.handler;

import com.gt06.model.*;
import com.gt06.protocol.ProtocolEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 协议包处理器
 * 负责处理各种类型的协议包并生成相应的响应
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PacketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(PacketHandler.class);
    
    /** 设备会话管理 */
    private final ConcurrentHashMap<String, DeviceSession> deviceSessions = new ConcurrentHashMap<>();
    
    /** 序列号生成器 */
    private final AtomicInteger serialNumberGenerator = new AtomicInteger(1);
    
    /**
     * 处理协议包
     * 
     * @param packet 协议包
     * @param connectionId 连接ID
     * @return 响应数据，如果不需要响应返回null
     */
    public byte[] handlePacket(ProtocolPacket packet, int connectionId) {
        if (packet == null) {
            logger.warn("协议包为空");
            return null;
        }
        
        try {
            logger.info("处理协议包 [连接{}]: {}", connectionId, packet);
            
            // 根据协议号分发处理
            switch (packet.getProtocolNumber()) {
                case LoginPacket.PROTOCOL_NUMBER:
                    return handleLoginPacket((LoginPacket) packet, connectionId);
                    
                case LocationPacket.PROTOCOL_NUMBER:
                    return handleLocationPacket((LocationPacket) packet, connectionId);
                    
                case HeartbeatPacket.PROTOCOL_NUMBER:
                    return handleHeartbeatPacket((HeartbeatPacket) packet, connectionId);
                    
                case AlarmPacket.PROTOCOL_NUMBER:
                    return handleAlarmPacket((AlarmPacket) packet, connectionId);
                    
                case CommandResponsePacket.PROTOCOL_NUMBER:
                    return handleCommandResponsePacket((CommandResponsePacket) packet, connectionId);
                    
                default:
                    logger.warn("不支持的协议号: 0x{}", String.format("%02X", packet.getProtocolNumber()));
                    return null;
            }
            
        } catch (Exception e) {
            logger.error("处理协议包时发生异常", e);
            return null;
        }
    }
    
    /**
     * 处理登录包
     */
    private byte[] handleLoginPacket(LoginPacket packet, int connectionId) {
        logger.info("处理设备登录 [连接{}]: IMEI={}", connectionId, packet.getImei());
        
        // 创建或更新设备会话
        String imei = packet.getImei();
        DeviceSession session = new DeviceSession(imei, connectionId);
        deviceSessions.put(imei, session);
        
        // 记录设备信息
        logger.info("设备登录成功 - IMEI: {}, 型号: {}, 时区: {}", 
                   packet.getImei(), packet.getModelIdentification(), packet.getTimeZone());
        
        // 生成登录响应
        return ProtocolEncoder.createLoginResponse(packet);
    }
    
    /**
     * 处理定位数据包
     */
    private byte[] handleLocationPacket(LocationPacket packet, int connectionId) {
        logger.info("处理定位数据 [连接{}]: 位置=({}, {}), 速度={}km/h", 
                   connectionId, packet.getLatitude(), packet.getLongitude(), packet.getSpeed());
        
        // 更新设备会话的最后活动时间
        updateSessionActivity(connectionId);
        
        // 存储定位数据（这里可以添加数据库存储逻辑）
        storeLocationData(packet, connectionId);
        
        // 生成定位数据响应
        return ProtocolEncoder.createLocationResponse(packet);
    }
    
    /**
     * 处理心跳包
     */
    private byte[] handleHeartbeatPacket(HeartbeatPacket packet, int connectionId) {
        logger.info("处理心跳数据 [连接{}]: GPS定位={}, 报警={}, GSM信号={}%", 
                   connectionId, 
                   packet.isGpsPositioned() ? "是" : "否",
                   packet.getAlarmType().getDescription(),
                   packet.getGsmSignalPercentage());
        
        // 更新设备会话的最后活动时间
        updateSessionActivity(connectionId);
        
        // 存储心跳数据
        storeHeartbeatData(packet, connectionId);
        
        // 生成心跳响应
        return ProtocolEncoder.createHeartbeatResponse(packet);
    }
    
    /**
     * 处理报警包
     */
    private byte[] handleAlarmPacket(AlarmPacket packet, int connectionId) {
        logger.warn("处理报警数据 [连接{}]: 报警类型={}, 位置=({}, {})", 
                   connectionId, 
                   packet.getAlarmType().getDescription(),
                   packet.getLatitude(), 
                   packet.getLongitude());
        
        // 更新设备会话的最后活动时间
        updateSessionActivity(connectionId);
        
        // 处理报警逻辑（发送通知、记录日志等）
        processAlarm(packet, connectionId);
        
        // 生成报警响应
        return ProtocolEncoder.createAlarmResponse(packet);
    }
    
    /**
     * 处理指令响应包
     */
    private byte[] handleCommandResponsePacket(CommandResponsePacket packet, int connectionId) {
        logger.info("处理指令响应 [连接{}]: 状态={}, 内容={}", 
                   connectionId, 
                   packet.getStatus().getDescription(),
                   packet.getResponseContent());
        
        // 更新设备会话的最后活动时间
        updateSessionActivity(connectionId);
        
        // 处理指令响应
        processCommandResponse(packet, connectionId);
        
        // 指令响应通常不需要服务器再次响应
        return null;
    }
    
    /**
     * 更新设备会话活动时间
     */
    private void updateSessionActivity(int connectionId) {
        deviceSessions.values().forEach(session -> {
            if (session.getConnectionId() == connectionId) {
                session.updateLastActivity();
            }
        });
    }
    
    /**
     * 存储定位数据
     */
    private void storeLocationData(LocationPacket packet, int connectionId) {
        // TODO: 实现数据库存储逻辑
        logger.debug("存储定位数据: 连接ID={}, 经度={}, 纬度={}, 时间={}", 
                    connectionId, packet.getLongitude(), packet.getLatitude(), packet.getDateTime());
    }
    
    /**
     * 存储心跳数据
     */
    private void storeHeartbeatData(HeartbeatPacket packet, int connectionId) {
        // TODO: 实现数据库存储逻辑
        logger.debug("存储心跳数据: 连接ID={}, GSM信号={}%, 电压等级={}", 
                    connectionId, packet.getGsmSignalPercentage(), packet.getVoltageLevelDescription());
    }
    
    /**
     * 处理报警
     */
    private void processAlarm(AlarmPacket packet, int connectionId) {
        // TODO: 实现报警处理逻辑（发送邮件、短信、推送通知等）
        logger.info("处理报警: 连接ID={}, 报警类型={}, 位置=({}, {})", 
                   connectionId, packet.getAlarmType().getDescription(),
                   packet.getLatitude(), packet.getLongitude());
    }
    
    /**
     * 处理指令响应
     */
    private void processCommandResponse(CommandResponsePacket packet, int connectionId) {
        // TODO: 实现指令响应处理逻辑
        logger.info("处理指令响应: 连接ID={}, 状态={}, 内容={}", 
                   connectionId, packet.getStatus().getDescription(), packet.getResponseContent());
    }
    
    /**
     * 发送指令到设备
     * 
     * @param imei 设备IMEI
     * @param command 指令内容
     * @return 是否发送成功
     */
    public boolean sendCommandToDevice(String imei, String command) {
        DeviceSession session = deviceSessions.get(imei);
        if (session == null) {
            logger.warn("设备未在线: {}", imei);
            return false;
        }
        
        try {
            int serialNumber = serialNumberGenerator.getAndIncrement();
            byte[] commandPacket = ProtocolEncoder.createCommandPacket(serialNumber, command);
            
            // TODO: 通过连接发送指令包
            logger.info("向设备 {} 发送指令: {}", imei, command);
            
            return true;
        } catch (Exception e) {
            logger.error("发送指令失败", e);
            return false;
        }
    }
    
    /**
     * 获取在线设备列表
     */
    public ConcurrentHashMap<String, DeviceSession> getOnlineDevices() {
        return new ConcurrentHashMap<>(deviceSessions);
    }
    
    /**
     * 设备会话类
     */
    public static class DeviceSession {
        private final String imei;
        private final int connectionId;
        private long lastActivity;
        private long loginTime;
        
        public DeviceSession(String imei, int connectionId) {
            this.imei = imei;
            this.connectionId = connectionId;
            this.loginTime = System.currentTimeMillis();
            this.lastActivity = System.currentTimeMillis();
        }
        
        public void updateLastActivity() {
            this.lastActivity = System.currentTimeMillis();
        }
        
        public String getImei() { return imei; }
        public int getConnectionId() { return connectionId; }
        public long getLastActivity() { return lastActivity; }
        public long getLoginTime() { return loginTime; }
        
        public boolean isActive(long timeoutMs) {
            return System.currentTimeMillis() - lastActivity < timeoutMs;
        }
    }
}