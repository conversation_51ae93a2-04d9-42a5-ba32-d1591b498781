package com.gt06.jdk21;

import com.gt06.model.ProtocolPacket;
import com.gt06.model.LoginPacket;
import com.gt06.model.LocationPacket;
import com.gt06.model.HeartbeatPacket;
import com.gt06.model.AlarmPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.StructuredTaskScope;
import java.util.concurrent.Future;
import java.time.Instant;
import java.time.Duration;

/**
 * 基于JDK21特性的现代化协议处理器
 * 展示虚拟线程、结构化并发、模式匹配、记录类等新特性
 * 
 * <AUTHOR>
 * @version 2.1.0
 */
public class ModernProtocolHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ModernProtocolHandler.class);
    
    /**
     * 协议处理结果记录类
     */
    public sealed interface ProcessResult 
        permits ProcessResult.Success, ProcessResult.Failure, ProcessResult.Warning {
        
        record Success(String message, Instant timestamp) implements ProcessResult {}
        record Failure(String error, Throwable cause, Instant timestamp) implements ProcessResult {}
        record Warning(String warning, String suggestion, Instant timestamp) implements ProcessResult {}
    }
    
    /**
     * 设备状态记录类
     */
    public record DeviceStatus(
        String deviceId,
        boolean online,
        Instant lastHeartbeat,
        double latitude,
        double longitude,
        int signalStrength,
        DeviceType type
    ) {
        // 紧凑构造器进行验证
        public DeviceStatus {
            if (deviceId == null || deviceId.isBlank()) {
                throw new IllegalArgumentException("设备ID不能为空");
            }
            if (latitude < -90 || latitude > 90) {
                throw new IllegalArgumentException("纬度必须在-90到90之间");
            }
            if (longitude < -180 || longitude > 180) {
                throw new IllegalArgumentException("经度必须在-180到180之间");
            }
        }
        
        // 便利方法
        public boolean isLocationValid() {
            return latitude != 0.0 && longitude != 0.0;
        }
        
        public Duration timeSinceLastHeartbeat() {
            return Duration.between(lastHeartbeat, Instant.now());
        }
    }
    
    /**
     * 设备类型枚举
     */
    public enum DeviceType {
        GPS_TRACKER("GPS定位器"),
        VEHICLE_MONITOR("车载监控"),
        ASSET_TRACKER("资产跟踪器"),
        PERSONAL_TRACKER("个人跟踪器");
        
        private final String description;
        
        DeviceType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 使用模式匹配和switch表达式处理协议包
     */
    public ProcessResult processPacket(ProtocolPacket packet) {
        var startTime = Instant.now();
        
        try {
            return switch (packet) {
                case LoginPacket login -> {
                    logger.info("处理登录包: 设备ID={}", login.getDeviceId());
                    
                    // 使用文本块构建响应消息
                    String message = """
                        登录处理完成:
                        - 设备ID: %s
                        - 登录时间: %s
                        - 处理耗时: %d ms
                        """.formatted(
                            login.getDeviceId(),
                            startTime,
                            Duration.between(startTime, Instant.now()).toMillis()
                        );
                    
                    yield new ProcessResult.Success(message, startTime);
                }
                
                case LocationPacket location -> {
                    logger.info("处理定位包: 设备ID={}, 位置=({}, {})", 
                        location.getDeviceId(), location.getLatitude(), location.getLongitude());
                    
                    // 验证位置数据
                    if (location.getLatitude() == 0.0 && location.getLongitude() == 0.0) {
                        yield new ProcessResult.Warning(
                            "位置数据无效", 
                            "建议检查GPS信号或设备配置", 
                            startTime
                        );
                    }
                    
                    yield new ProcessResult.Success(
                        "定位数据处理完成: " + location.getDeviceId(), 
                        startTime
                    );
                }
                
                case HeartbeatPacket heartbeat -> {
                    logger.debug("处理心跳包: 设备ID={}", heartbeat.getDeviceId());
                    yield new ProcessResult.Success(
                        "心跳包处理完成: " + heartbeat.getDeviceId(), 
                        startTime
                    );
                }
                
                case AlarmPacket alarm -> {
                    logger.warn("处理报警包: 设备ID={}, 报警类型={}", 
                        alarm.getDeviceId(), alarm.getAlarmType());
                    
                    // 根据报警类型采取不同处理策略
                    String action = switch (alarm.getAlarmType()) {
                        case 1 -> "SOS紧急报警 - 立即通知相关人员";
                        case 2 -> "超速报警 - 记录违规行为";
                        case 3 -> "区域报警 - 检查是否越界";
                        case 4 -> "断电报警 - 检查设备电源状态";
                        default -> "未知报警类型 - 需要进一步分析";
                    };
                    
                    yield new ProcessResult.Success(
                        "报警处理完成: " + action, 
                        startTime
                    );
                }
                
                case null -> new ProcessResult.Failure(
                    "协议包为空", 
                    new IllegalArgumentException("packet is null"), 
                    startTime
                );
                
                default -> new ProcessResult.Warning(
                    "未知协议包类型: " + packet.getClass().getSimpleName(),
                    "建议检查协议版本或添加新的处理逻辑",
                    startTime
                );
            };
            
        } catch (Exception e) {
            logger.error("处理协议包时发生异常", e);
            return new ProcessResult.Failure(
                "协议包处理失败: " + e.getMessage(), 
                e, 
                startTime
            );
        }
    }
    
    /**
     * 使用结构化并发批量处理协议包
     */
    public void processBatchPackets(ProtocolPacket... packets) {
        logger.info("开始批量处理 {} 个协议包", packets.length);
        
        try (var scope = new StructuredTaskScope.ShutdownOnFailure()) {
            // 为每个协议包创建虚拟线程进行处理
            Future<ProcessResult>[] futures = new Future[packets.length];
            
            for (int i = 0; i < packets.length; i++) {
                final int index = i;
                final ProtocolPacket packet = packets[i];
                
                futures[i] = scope.fork(() -> {
                    Thread.currentThread().setName("PacketProcessor-" + index);
                    return processPacket(packet);
                });
            }
            
            // 等待所有任务完成
            scope.join();
            scope.throwIfFailed();
            
            // 收集结果
            for (int i = 0; i < futures.length; i++) {
                ProcessResult result = futures[i].resultNow();
                
                switch (result) {
                    case ProcessResult.Success success -> 
                        logger.info("包 {} 处理成功: {}", i, success.message());
                    
                    case ProcessResult.Warning warning -> 
                        logger.warn("包 {} 处理警告: {} (建议: {})", 
                            i, warning.warning(), warning.suggestion());
                    
                    case ProcessResult.Failure failure -> 
                        logger.error("包 {} 处理失败: {}", i, failure.error(), failure.cause());
                }
            }
            
            logger.info("批量处理完成");
            
        } catch (InterruptedException e) {
            logger.error("批量处理被中断", e);
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 使用虚拟线程进行异步设备状态监控
     */
    public void startDeviceMonitoring(DeviceStatus... devices) {
        logger.info("开始监控 {} 个设备", devices.length);
        
        for (DeviceStatus device : devices) {
            // 为每个设备创建专用的虚拟线程
            Thread.ofVirtual()
                .name("DeviceMonitor-" + device.deviceId())
                .start(() -> monitorDevice(device));
        }
    }
    
    /**
     * 单个设备监控逻辑
     */
    private void monitorDevice(DeviceStatus device) {
        logger.info("开始监控设备: {}", device.deviceId());
        
        while (!Thread.currentThread().isInterrupted()) {
            try {
                // 检查设备状态
                Duration timeSinceHeartbeat = device.timeSinceLastHeartbeat();
                
                if (timeSinceHeartbeat.toMinutes() > 5) {
                    logger.warn("设备 {} 超过5分钟未发送心跳包", device.deviceId());
                }
                
                if (!device.isLocationValid()) {
                    logger.warn("设备 {} 位置数据无效", device.deviceId());
                }
                
                // 根据设备类型执行不同的监控策略
                String monitorAction = switch (device.type()) {
                    case GPS_TRACKER -> "检查GPS信号强度和定位精度";
                    case VEHICLE_MONITOR -> "监控车辆状态和驾驶行为";
                    case ASSET_TRACKER -> "跟踪资产位置和状态变化";
                    case PERSONAL_TRACKER -> "监控个人安全和位置信息";
                };
                
                logger.debug("设备 {} 监控动作: {}", device.deviceId(), monitorAction);
                
                // 休眠30秒后继续监控
                Thread.sleep(Duration.ofSeconds(30));
                
            } catch (InterruptedException e) {
                logger.info("设备 {} 监控被中断", device.deviceId());
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("监控设备 {} 时发生异常", device.deviceId(), e);
            }
        }
        
        logger.info("设备 {} 监控结束", device.deviceId());
    }
    
    /**
     * 演示JDK21的其他新特性
     */
    public void demonstrateJDK21Features() {
        // 使用文本块和字符串模板
        String info = """
            JDK21新特性演示:
            - 虚拟线程 (Virtual Threads)
            - 结构化并发 (Structured Concurrency)  
            - 模式匹配 (Pattern Matching)
            - 记录类 (Record Classes)
            - 密封类 (Sealed Classes)
            - 文本块 (Text Blocks)
            - Switch表达式增强
            """;
        
        logger.info(info);
        
        // 演示记录类的解构
        DeviceStatus device = new DeviceStatus(
            "GT06-001", 
            true, 
            Instant.now(), 
            39.9042, 
            116.4074, 
            85, 
            DeviceType.GPS_TRACKER
        );
        
        // 使用记录类的便利方法
        logger.info("设备位置有效: {}", device.isLocationValid());
        logger.info("距离上次心跳: {} 秒", device.timeSinceLastHeartbeat().getSeconds());
        
        // 演示密封类的模式匹配
        ProcessResult result = new ProcessResult.Success("演示成功", Instant.now());
        
        String resultMessage = switch (result) {
            case ProcessResult.Success(var message, var timestamp) -> 
                "成功: " + message + " (时间: " + timestamp + ")";
            case ProcessResult.Failure(var error, var cause, var timestamp) -> 
                "失败: " + error + " (原因: " + cause + ", 时间: " + timestamp + ")";
            case ProcessResult.Warning(var warning, var suggestion, var timestamp) -> 
                "警告: " + warning + " (建议: " + suggestion + ", 时间: " + timestamp + ")";
        };
        
        logger.info("处理结果: {}", resultMessage);
    }
}