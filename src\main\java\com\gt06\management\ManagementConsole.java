package com.gt06.management;

import com.gt06.monitor.ServerMetrics;
import com.gt06.alert.AlertManager;
import com.gt06.fault.FaultTolerance;
import com.gt06.performance.PerformanceOptimizer;
import com.gt06.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;

/**
 * 管理控制台
 * 提供Web界面用于监控和管理GT06服务器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ManagementConsole {
    
    private static final Logger logger = LoggerFactory.getLogger(ManagementConsole.class);
    
    /** 单例实例 */
    private static volatile ManagementConsole instance;
    
    /** HTTP服务器 */
    private HttpServer httpServer;
    
    /** 管理端口 */
    private final int managementPort;
    
    /** 运行状态 */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /** 定时任务执行器 */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    /** 组件引用 */
    private final ServerMetrics serverMetrics;
    private final AlertManager alertManager;
    private final FaultTolerance faultTolerance;
    private final PerformanceOptimizer performanceOptimizer;
    private final ConfigManager configManager;
    
    /** 日期格式化器 */
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    private ManagementConsole() {
        this.configManager = ConfigManager.getInstance();
        this.managementPort = configManager.getInt("management.port", 8842);
        
        this.serverMetrics = ServerMetrics.getInstance();
        this.alertManager = AlertManager.getInstance();
        this.faultTolerance = FaultTolerance.getInstance();
        this.performanceOptimizer = PerformanceOptimizer.getInstance();
    }
    
    /**
     * 获取单例实例
     */
    public static ManagementConsole getInstance() {
        if (instance == null) {
            synchronized (ManagementConsole.class) {
                if (instance == null) {
                    instance = new ManagementConsole();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动管理控制台
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            try {
                // 创建HTTP服务器
                httpServer = HttpServer.create(new InetSocketAddress(managementPort), 0);
                
                // 注册处理器
                registerHandlers();
                
                // 设置执行器
                httpServer.setExecutor(Executors.newFixedThreadPool(5));
                
                // 启动服务器
                httpServer.start();
                
                logger.info("管理控制台已启动，访问地址: http://localhost:{}", managementPort);
                
            } catch (IOException e) {
                logger.error("启动管理控制台失败", e);
                running.set(false);
            }
        }
    }
    
    /**
     * 停止管理控制台
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            if (httpServer != null) {
                httpServer.stop(5);
            }
            
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            logger.info("管理控制台已停止");
        }
    }
    
    /**
     * 注册HTTP处理器
     */
    private void registerHandlers() {
        // 主页
        httpServer.createContext("/", new DashboardHandler());
        
        // API接口
        httpServer.createContext("/api/metrics", new MetricsHandler());
        httpServer.createContext("/api/alerts", new AlertsHandler());
        httpServer.createContext("/api/config", new ConfigHandler());
        httpServer.createContext("/api/performance", new PerformanceHandler());
        httpServer.createContext("/api/health", new HealthHandler());
        
        // 静态资源
        httpServer.createContext("/static", new StaticResourceHandler());
    }
    
    /**
     * 仪表板处理器
     */
    private class DashboardHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("GET".equals(exchange.getRequestMethod())) {
                String response = generateDashboardHtml();
                sendResponse(exchange, 200, response, "text/html");
            } else {
                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
            }
        }
        
        private String generateDashboardHtml() {
            ServerMetrics.MetricsSnapshot snapshot = serverMetrics.getSnapshot();
            
            StringBuilder html = new StringBuilder();
            html.append("<!DOCTYPE html>\n");
            html.append("<html lang='zh-CN'>\n");
            html.append("<head>\n");
            html.append("    <meta charset='UTF-8'>\n");
            html.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n");
            html.append("    <title>GT06服务器管理控制台</title>\n");
            html.append("    <style>\n");
            html.append("        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n");
            html.append("        .container { max-width: 1200px; margin: 0 auto; }\n");
            html.append("        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }\n");
            html.append("        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }\n");
            html.append("        .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n");
            html.append("        .metric-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #2c3e50; }\n");
            html.append("        .metric-value { font-size: 24px; font-weight: bold; color: #3498db; }\n");
            html.append("        .metric-label { font-size: 14px; color: #7f8c8d; margin-top: 5px; }\n");
            html.append("        .status-good { color: #27ae60; }\n");
            html.append("        .status-warning { color: #f39c12; }\n");
            html.append("        .status-error { color: #e74c3c; }\n");
            html.append("        .refresh-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }\n");
            html.append("        .refresh-btn:hover { background: #2980b9; }\n");
            html.append("        table { width: 100%; border-collapse: collapse; margin-top: 10px; }\n");
            html.append("        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }\n");
            html.append("        th { background-color: #f8f9fa; }\n");
            html.append("    </style>\n");
            html.append("    <script>\n");
            html.append("        function refreshPage() { location.reload(); }\n");
            html.append("        setInterval(refreshPage, 30000); // 30秒自动刷新\n");
            html.append("    </script>\n");
            html.append("</head>\n");
            html.append("<body>\n");
            html.append("    <div class='container'>\n");
            html.append("        <div class='header'>\n");
            html.append("            <h1>GT06服务器管理控制台</h1>\n");
            html.append("            <p>实时监控和管理GT06协议服务器</p>\n");
            html.append("            <button class='refresh-btn' onclick='refreshPage()'>刷新数据</button>\n");
            html.append("            <span style='float: right;'>最后更新: ").append(dateFormat.format(new Date())).append("</span>\n");
            html.append("        </div>\n");
            
            // 系统概览
            html.append("        <div class='metrics-grid'>\n");
            
            // 连接统计
            html.append("            <div class='metric-card'>\n");
            html.append("                <div class='metric-title'>连接统计</div>\n");
            html.append("                <div class='metric-value'>").append(snapshot.getActiveConnections()).append("</div>\n");
            html.append("                <div class='metric-label'>当前活跃连接</div>\n");
            html.append("                <div style='margin-top: 10px;'>\n");
            html.append("                    <div>总连接数: ").append(snapshot.getTotalConnections()).append("</div>\n");
            html.append("                    <div>在线设备: ").append(snapshot.getOnlineDevices()).append("</div>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            // 数据包统计
            html.append("            <div class='metric-card'>\n");
            html.append("                <div class='metric-title'>数据包统计</div>\n");
            html.append("                <div class='metric-value'>").append(snapshot.getTotalPacketsReceived()).append("</div>\n");
            html.append("                <div class='metric-label'>接收数据包总数</div>\n");
            html.append("                <div style='margin-top: 10px;'>\n");
            html.append("                    <div>发送数据包: ").append(snapshot.getTotalPacketsSent()).append("</div>\n");
            html.append("                    <div>当前QPS: ").append(String.format("%.2f", snapshot.getCurrentQPS())).append("</div>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            // 性能指标
            html.append("            <div class='metric-card'>\n");
            html.append("                <div class='metric-title'>性能指标</div>\n");
            html.append("                <div class='metric-value'>").append(String.format("%.2f", snapshot.getAverageProcessingTime())).append("ms</div>\n");
            html.append("                <div class='metric-label'>平均处理时间</div>\n");
            html.append("                <div style='margin-top: 10px;'>\n");
            html.append("                    <div>最大处理时间: ").append(snapshot.getMaxProcessingTime()).append("ms</div>\n");
            html.append("                    <div>运行时间: ").append(formatUptime(snapshot.getUptime())).append("</div>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            // 错误统计
            String errorClass = snapshot.getErrorRate() > 5.0 ? "status-error" : 
                               snapshot.getErrorRate() > 1.0 ? "status-warning" : "status-good";
            html.append("            <div class='metric-card'>\n");
            html.append("                <div class='metric-title'>错误统计</div>\n");
            html.append("                <div class='metric-value ").append(errorClass).append("'>").append(String.format("%.2f", snapshot.getErrorRate())).append("%</div>\n");
            html.append("                <div class='metric-label'>错误率</div>\n");
            html.append("                <div style='margin-top: 10px;'>\n");
            html.append("                    <div>CRC错误: ").append(snapshot.getCrcErrors()).append("</div>\n");
            html.append("                    <div>解码错误: ").append(snapshot.getDecodeErrors()).append("</div>\n");
            html.append("                    <div>连接错误: ").append(snapshot.getConnectionErrors()).append("</div>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            // 告警状态
            long activeAlerts = alertManager.getActiveAlertCount();
            String alertClass = activeAlerts > 0 ? "status-error" : "status-good";
            html.append("            <div class='metric-card'>\n");
            html.append("                <div class='metric-title'>告警状态</div>\n");
            html.append("                <div class='metric-value ").append(alertClass).append("'>").append(activeAlerts).append("</div>\n");
            html.append("                <div class='metric-label'>活跃告警数量</div>\n");
            html.append("                <div style='margin-top: 10px;'>\n");
            html.append("                    <a href='/api/alerts' target='_blank'>查看详细告警信息</a>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            // 内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            double memoryUsage = (double) usedMemory / maxMemory * 100;
            String memoryClass = memoryUsage > 85 ? "status-error" : 
                                memoryUsage > 70 ? "status-warning" : "status-good";
            html.append("            <div class='metric-card'>\n");
            html.append("                <div class='metric-title'>内存使用</div>\n");
            html.append("                <div class='metric-value ").append(memoryClass).append("'>").append(String.format("%.1f", memoryUsage)).append("%</div>\n");
            html.append("                <div class='metric-label'>内存使用率</div>\n");
            html.append("                <div style='margin-top: 10px;'>\n");
            html.append("                    <div>已用: ").append(formatBytes(usedMemory)).append("</div>\n");
            html.append("                    <div>最大: ").append(formatBytes(maxMemory)).append("</div>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            
            html.append("        </div>\n");
            
            // 协议包类型统计表
            if (!snapshot.getPacketTypeStats().isEmpty()) {
                html.append("        <div class='metric-card' style='margin-top: 20px;'>\n");
                html.append("            <div class='metric-title'>协议包类型统计</div>\n");
                html.append("            <table>\n");
                html.append("                <tr><th>协议号</th><th>协议名称</th><th>数量</th><th>占比</th></tr>\n");
                
                long totalPackets = snapshot.getTotalPacketsReceived();
                snapshot.getPacketTypeStats().forEach((type, count) -> {
                    String protocolName = getProtocolName(type);
                    double percentage = totalPackets > 0 ? (double) count.get() / totalPackets * 100 : 0;
                    html.append("                <tr>\n");
                    html.append("                    <td>0x").append(String.format("%02X", type)).append("</td>\n");
                    html.append("                    <td>").append(protocolName).append("</td>\n");
                    html.append("                    <td>").append(count.get()).append("</td>\n");
                    html.append("                    <td>").append(String.format("%.2f", percentage)).append("%</td>\n");
                    html.append("                </tr>\n");
                });
                
                html.append("            </table>\n");
                html.append("        </div>\n");
            }
            
            html.append("    </div>\n");
            html.append("</body>\n");
            html.append("</html>\n");
            
            return html.toString();
        }
    }
    
    /**
     * 指标API处理器
     */
    private class MetricsHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("GET".equals(exchange.getRequestMethod())) {
                ServerMetrics.MetricsSnapshot snapshot = serverMetrics.getSnapshot();
                String json = convertMetricsToJson(snapshot);
                sendResponse(exchange, 200, json, "application/json");
            } else {
                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
            }
        }
        
        private String convertMetricsToJson(ServerMetrics.MetricsSnapshot snapshot) {
            StringBuilder json = new StringBuilder();
            json.append("{\n");
            json.append("  \"totalConnections\": ").append(snapshot.getTotalConnections()).append(",\n");
            json.append("  \"activeConnections\": ").append(snapshot.getActiveConnections()).append(",\n");
            json.append("  \"totalPacketsReceived\": ").append(snapshot.getTotalPacketsReceived()).append(",\n");
            json.append("  \"totalPacketsSent\": ").append(snapshot.getTotalPacketsSent()).append(",\n");
            json.append("  \"currentQPS\": ").append(snapshot.getCurrentQPS()).append(",\n");
            json.append("  \"averageProcessingTime\": ").append(snapshot.getAverageProcessingTime()).append(",\n");
            json.append("  \"maxProcessingTime\": ").append(snapshot.getMaxProcessingTime()).append(",\n");
            json.append("  \"errorRate\": ").append(snapshot.getErrorRate()).append(",\n");
            json.append("  \"crcErrors\": ").append(snapshot.getCrcErrors()).append(",\n");
            json.append("  \"decodeErrors\": ").append(snapshot.getDecodeErrors()).append(",\n");
            json.append("  \"connectionErrors\": ").append(snapshot.getConnectionErrors()).append(",\n");
            json.append("  \"uptime\": ").append(snapshot.getUptime()).append(",\n");
            json.append("  \"onlineDevices\": ").append(snapshot.getOnlineDevices()).append(",\n");
            json.append("  \"timestamp\": ").append(System.currentTimeMillis()).append("\n");
            json.append("}\n");
            return json.toString();
        }
    }
    
    /**
     * 告警API处理器
     */
    private class AlertsHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("GET".equals(exchange.getRequestMethod())) {
                Map<String, AlertManager.AlertStatus> alerts = alertManager.getCurrentAlertStatus();
                String json = convertAlertsToJson(alerts);
                sendResponse(exchange, 200, json, "application/json");
            } else {
                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
            }
        }
        
        private String convertAlertsToJson(Map<String, AlertManager.AlertStatus> alerts) {
            StringBuilder json = new StringBuilder();
            json.append("{\n");
            json.append("  \"activeAlerts\": ").append(alertManager.getActiveAlertCount()).append(",\n");
            json.append("  \"alerts\": [\n");
            
            boolean first = true;
            for (Map.Entry<String, AlertManager.AlertStatus> entry : alerts.entrySet()) {
                if (!first) json.append(",\n");
                first = false;
                
                AlertManager.AlertStatus status = entry.getValue();
                json.append("    {\n");
                json.append("      \"ruleId\": \"").append(status.getRuleId()).append("\",\n");
                json.append("      \"status\": \"").append(status.getStatus()).append("\",\n");
                json.append("      \"level\": \"").append(status.getLevel()).append("\",\n");
                json.append("      \"triggeredTime\": ").append(status.getTriggeredTime()).append(",\n");
                json.append("      \"lastCheckTime\": ").append(status.getLastCheckTime()).append("\n");
                json.append("    }");
            }
            
            json.append("\n  ],\n");
            json.append("  \"timestamp\": ").append(System.currentTimeMillis()).append("\n");
            json.append("}\n");
            return json.toString();
        }
    }
    
    /**
     * 配置API处理器
     */
    private class ConfigHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String method = exchange.getRequestMethod();
            
            if ("GET".equals(method)) {
                // 获取配置
                String json = convertConfigToJson();
                sendResponse(exchange, 200, json, "application/json");
            } else if ("POST".equals(method)) {
                // 更新配置（这里可以扩展配置更新功能）
                sendResponse(exchange, 200, "{\"status\": \"success\"}", "application/json");
            } else {
                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
            }
        }
        
        private String convertConfigToJson() {
            ConfigManager.ServerConfig serverConfig = configManager.getServerConfig();
            ConfigManager.MonitorConfig monitorConfig = configManager.getMonitorConfig();
            ConfigManager.AlertConfig alertConfig = configManager.getAlertConfig();
            
            StringBuilder json = new StringBuilder();
            json.append("{\n");
            json.append("  \"server\": {\n");
            json.append("    \"port\": ").append(serverConfig.getPort()).append(",\n");
            json.append("    \"threadPoolSize\": ").append(serverConfig.getThreadPoolSize()).append(",\n");
            json.append("    \"connectionTimeout\": ").append(serverConfig.getConnectionTimeout()).append(",\n");
            json.append("    \"maxConnections\": ").append(serverConfig.getMaxConnections()).append("\n");
            json.append("  },\n");
            json.append("  \"monitor\": {\n");
            json.append("    \"enabled\": ").append(monitorConfig.isEnabled()).append(",\n");
            json.append("    \"reportInterval\": ").append(monitorConfig.getReportInterval()).append("\n");
            json.append("  },\n");
            json.append("  \"alert\": {\n");
            json.append("    \"enabled\": ").append(alertConfig.isEnabled()).append(",\n");
            json.append("    \"errorRateThreshold\": ").append(alertConfig.getErrorRateThreshold()).append(",\n");
            json.append("    \"connectionThreshold\": ").append(alertConfig.getConnectionThreshold()).append(",\n");
            json.append("    \"processingTimeThreshold\": ").append(alertConfig.getProcessingTimeThreshold()).append("\n");
            json.append("  }\n");
            json.append("}\n");
            return json.toString();
        }
    }
    
    /**
     * 性能API处理器
     */
    private class PerformanceHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("GET".equals(exchange.getRequestMethod())) {
                String json = convertPerformanceToJson();
                sendResponse(exchange, 200, json, "application/json");
            } else {
                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
            }
        }
        
        private String convertPerformanceToJson() {
            PerformanceOptimizer.MemoryManager.MemoryStatus memoryStatus = 
                performanceOptimizer.getMemoryManager().getMemoryStatus();
            
            StringBuilder json = new StringBuilder();
            json.append("{\n");
            json.append("  \"memory\": {\n");
            json.append("    \"maxMemory\": ").append(memoryStatus.getMaxMemory()).append(",\n");
            json.append("    \"totalMemory\": ").append(memoryStatus.getTotalMemory()).append(",\n");
            json.append("    \"freeMemory\": ").append(memoryStatus.getFreeMemory()).append(",\n");
            json.append("    \"usedMemory\": ").append(memoryStatus.getUsedMemory()).append(",\n");
            json.append("    \"memoryUsage\": ").append(memoryStatus.getMemoryUsage()).append(",\n");
            json.append("    \"gcCount\": ").append(memoryStatus.getGcCount()).append("\n");
            json.append("  }\n");
            json.append("}\n");
            return json.toString();
        }
    }
    
    /**
     * 健康检查处理器
     */
    private class HealthHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if ("GET".equals(exchange.getRequestMethod())) {
                String json = "{\n  \"status\": \"healthy\",\n  \"timestamp\": " + System.currentTimeMillis() + "\n}\n";
                sendResponse(exchange, 200, json, "application/json");
            } else {
                sendResponse(exchange, 405, "Method Not Allowed", "text/plain");
            }
        }
    }
    
    /**
     * 静态资源处理器
     */
    private class StaticResourceHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            sendResponse(exchange, 404, "Not Found", "text/plain");
        }
    }
    
    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response, String contentType) throws IOException {
        exchange.getResponseHeaders().set("Content-Type", contentType + "; charset=UTF-8");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.sendResponseHeaders(statusCode, response.getBytes("UTF-8").length);
        exchange.getResponseBody().write(response.getBytes("UTF-8"));
        exchange.getResponseBody().close();
    }
    
    /**
     * 格式化运行时间
     */
    private String formatUptime(long uptimeMs) {
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天%d小时", days, hours % 24);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes >= 1024 * 1024 * 1024) {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        } else if (bytes >= 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else if (bytes >= 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else {
            return bytes + " B";
        }
    }
    
    /**
     * 获取协议名称
     */
    private String getProtocolName(byte protocolNumber) {
        switch (protocolNumber) {
            case 0x01: return "登录包";
            case 0x12: return "定位数据包";
            case 0x13: return "心跳包";
            case 0x16: return "报警包";
            case (byte) 0x80: return "指令下发";
            case (byte) 0x81: return "指令响应";
            default: return "未知协议";
        }
    }
    
    /**
     * 获取管理端口
     */
    public int getManagementPort() {
        return managementPort;
    }
    
    /**
     * 检查是否运行中
     */
    public boolean isRunning() {
        return running.get();
    }
}