package com.gt06.model;

/**
 * 报警数据包（协议号：0x16）
 * 用于处理设备报警信息的数据包
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class AlarmPacket extends LocationPacket {
    
    /** 协议号 */
    public static final byte PROTOCOL_NUMBER = 0x16;
    
    /** 报警类型 */
    private AlarmType alarmType;
    
    /** 报警时间 */
    private long alarmTime;
    
    /** 报警描述 */
    private String alarmDescription;
    
    public AlarmPacket() {
        super();
        setProtocolNumber(PROTOCOL_NUMBER);
    }
    
    public AlarmPacket(int serialNumber) {
        super(serialNumber);
        setProtocolNumber(PROTOCOL_NUMBER);
    }
    
    @Override
    public void parseFromBytes(byte[] data, int offset) {
        // 先解析定位数据
        super.parseFromBytes(data, offset);
        
        // 解析报警特有信息
        if (data.length > offset + 21) { // 定位数据21字节后的报警信息
            parseAlarmInfo(data, offset + 21);
        }
    }
    
    /**
     * 解析报警信息
     */
    private void parseAlarmInfo(byte[] data, int offset) {
        if (data.length >= offset + 1) {
            byte alarmCode = data[offset];
            this.alarmType = parseAlarmType(alarmCode);
            this.alarmTime = System.currentTimeMillis();
            this.alarmDescription = alarmType.getDescription();
        }
    }
    
    /**
     * 解析报警类型
     */
    private AlarmType parseAlarmType(byte alarmCode) {
        switch (alarmCode & 0xFF) {
            case 0x01: return AlarmType.SOS_ALARM;
            case 0x02: return AlarmType.POWER_OFF_ALARM;
            case 0x03: return AlarmType.VIBRATION_ALARM;
            case 0x04: return AlarmType.FENCE_IN_ALARM;
            case 0x05: return AlarmType.FENCE_OUT_ALARM;
            case 0x06: return AlarmType.OVERSPEED_ALARM;
            case 0x09: return AlarmType.MOVE_ALARM;
            case 0x0A: return AlarmType.LOW_BATTERY_ALARM;
            case 0x0B: return AlarmType.POWER_CUT_ALARM;
            case 0x0C: return AlarmType.TEMPERATURE_ALARM;
            case 0x0D: return AlarmType.ACC_ON_ALARM;
            case 0x0E: return AlarmType.ACC_OFF_ALARM;
            case 0x13: return AlarmType.DOOR_ALARM;
            case 0x14: return AlarmType.SHOCK_ALARM;
            case 0x15: return AlarmType.COLLISION_ALARM;
            case 0x16: return AlarmType.ROLLOVER_ALARM;
            default: return AlarmType.UNKNOWN_ALARM;
        }
    }
    
    /**
     * 报警类型枚举
     */
    public enum AlarmType {
        SOS_ALARM("SOS紧急报警"),
        POWER_OFF_ALARM("断电报警"),
        VIBRATION_ALARM("震动报警"),
        FENCE_IN_ALARM("进入围栏报警"),
        FENCE_OUT_ALARM("离开围栏报警"),
        OVERSPEED_ALARM("超速报警"),
        MOVE_ALARM("移动报警"),
        LOW_BATTERY_ALARM("低电量报警"),
        POWER_CUT_ALARM("主电源断开报警"),
        TEMPERATURE_ALARM("温度报警"),
        ACC_ON_ALARM("ACC开启报警"),
        ACC_OFF_ALARM("ACC关闭报警"),
        DOOR_ALARM("车门报警"),
        SHOCK_ALARM("冲击报警"),
        COLLISION_ALARM("碰撞报警"),
        ROLLOVER_ALARM("翻车报警"),
        UNKNOWN_ALARM("未知报警");
        
        private final String description;
        
        AlarmType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Getter和Setter方法
    public AlarmType getAlarmType() { return alarmType; }
    public void setAlarmType(AlarmType alarmType) { this.alarmType = alarmType; }
    
    public long getAlarmTime() { return alarmTime; }
    public void setAlarmTime(long alarmTime) { this.alarmTime = alarmTime; }
    
    public String getAlarmDescription() { return alarmDescription; }
    public void setAlarmDescription(String alarmDescription) { this.alarmDescription = alarmDescription; }
    
    @Override
    public String toString() {
        return String.format("报警包 - 类型: %s, 位置: (%.6f, %.6f), %s", 
                           alarmType != null ? alarmType.getDescription() : "未知",
                           getLatitude(), getLongitude(),
                           super.toString());
    }
}