package com.gt06.model;

/**
 * 指令响应包（协议号：0x81）
 * 用于处理设备对服务器指令的响应
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class CommandResponsePacket extends ProtocolPacket {
    
    /** 协议号 */
    public static final byte PROTOCOL_NUMBER = (byte) 0x81;
    
    /** 响应内容 */
    private String responseContent;
    
    /** 响应状态 */
    private ResponseStatus status;
    
    /** 原始指令 */
    private String originalCommand;
    
    public CommandResponsePacket() {
        super(PROTOCOL_NUMBER, 0);
    }
    
    public CommandResponsePacket(int serialNumber) {
        super(PROTOCOL_NUMBER, serialNumber);
    }
    
    @Override
    public byte[] getInfoContent() {
        if (responseContent != null) {
            try {
                return responseContent.getBytes("UTF-8");
            } catch (Exception e) {
                return responseContent.getBytes();
            }
        }
        return new byte[0];
    }
    
    @Override
    public void parseFromBytes(byte[] data, int offset) {
        if (data.length > offset) {
            try {
                int contentLength = data.length - offset - 4; // 减去序列号(2)和CRC(2)
                if (contentLength > 0) {
                    byte[] contentBytes = new byte[contentLength];
                    System.arraycopy(data, offset, contentBytes, 0, contentLength);
                    this.responseContent = new String(contentBytes, "UTF-8");
                    parseResponseStatus();
                }
            } catch (Exception e) {
                // 使用默认编码
                int contentLength = data.length - offset - 4;
                if (contentLength > 0) {
                    byte[] contentBytes = new byte[contentLength];
                    System.arraycopy(data, offset, contentBytes, 0, contentLength);
                    this.responseContent = new String(contentBytes);
                    parseResponseStatus();
                }
            }
        }
    }
    
    /**
     * 解析响应状态
     */
    private void parseResponseStatus() {
        if (responseContent == null || responseContent.isEmpty()) {
            status = ResponseStatus.UNKNOWN;
            return;
        }
        
        String content = responseContent.toLowerCase();
        if (content.contains("ok") || content.contains("success")) {
            status = ResponseStatus.SUCCESS;
        } else if (content.contains("error") || content.contains("fail")) {
            status = ResponseStatus.ERROR;
        } else if (content.contains("timeout")) {
            status = ResponseStatus.TIMEOUT;
        } else {
            status = ResponseStatus.UNKNOWN;
        }
    }
    
    /**
     * 响应状态枚举
     */
    public enum ResponseStatus {
        SUCCESS("成功"),
        ERROR("错误"),
        TIMEOUT("超时"),
        UNKNOWN("未知");
        
        private final String description;
        
        ResponseStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // Getter和Setter方法
    public String getResponseContent() { return responseContent; }
    public void setResponseContent(String responseContent) { 
        this.responseContent = responseContent;
        parseResponseStatus();
    }
    
    public ResponseStatus getStatus() { return status; }
    public void setStatus(ResponseStatus status) { this.status = status; }
    
    public String getOriginalCommand() { return originalCommand; }
    public void setOriginalCommand(String originalCommand) { this.originalCommand = originalCommand; }
    
    @Override
    public String toString() {
        return String.format("指令响应包 - 状态: %s, 内容: %s, %s", 
                           status != null ? status.getDescription() : "未知",
                           responseContent != null ? responseContent : "无",
                           super.toString());
    }
}