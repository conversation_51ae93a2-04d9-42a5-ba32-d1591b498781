package com.gt06.model;

/**
 * 心跳包（状态信息包：0x13）
 * 用于维持终端与服务器连接的数据包
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class HeartbeatPacket extends ProtocolPacket {
    
    /** 协议号 */
    public static final byte PROTOCOL_NUMBER = 0x13;
    
    /** 终端信息 - 1字节 */
    private byte terminalInfo;
    
    /** 电压等级 - 1字节 */
    private byte voltageLevel;
    
    /** GSM信号强度 - 1字节 */
    private byte gsmSignalStrength;
    
    /** 外部电压 - 1字节 */
    private byte externalVoltage;
    
    /** 语言 - 1字节 */
    private byte language;
    
    public HeartbeatPacket() {
        super(PROTOCOL_NUMBER, 0);
    }
    
    public HeartbeatPacket(int serialNumber) {
        super(PROTOCOL_NUMBER, serialNumber);
    }
    
    @Override
    public byte[] getInfoContent() {
        return new byte[] {
            terminalInfo,
            voltageLevel,
            gsmSignalStrength,
            externalVoltage,
            language
        };
    }
    
    @Override
    public void parseFromBytes(byte[] data, int offset) {
        if (data.length >= offset + 5) {
            terminalInfo = data[offset];
            voltageLevel = data[offset + 1];
            gsmSignalStrength = data[offset + 2];
            externalVoltage = data[offset + 3];
            language = data[offset + 4];
        }
    }
    
    /**
     * 解析终端信息状态
     */
    
    /** 是否油电断开 */
    public boolean isOilElectricityDisconnected() {
        return (terminalInfo & 0x80) != 0; // Bit7
    }
    
    /** GPS是否已定位 */
    public boolean isGpsPositioned() {
        return (terminalInfo & 0x40) != 0; // Bit6
    }
    
    /** 获取报警类型 */
    public AlarmType getAlarmType() {
        int alarmBits = (terminalInfo >> 3) & 0x07; // Bit5-Bit3
        switch (alarmBits) {
            case 0: return AlarmType.NORMAL;
            case 1: return AlarmType.VIBRATION;
            case 2: return AlarmType.POWER_OFF;
            case 3: return AlarmType.LOW_BATTERY;
            case 4: return AlarmType.SOS;
            case 6: return AlarmType.DOOR_CLOSE;
            case 7: return AlarmType.DOOR_OPEN;
            default: return AlarmType.UNKNOWN;
        }
    }
    
    /** 是否已接外电源 */
    public boolean isExternalPowerConnected() {
        return (terminalInfo & 0x04) != 0; // Bit2
    }
    
    /** ACC是否开启 */
    public boolean isAccOn() {
        return (terminalInfo & 0x02) != 0; // Bit1
    }
    
    /** 是否设防 */
    public boolean isArmed() {
        return (terminalInfo & 0x01) != 0; // Bit0
    }
    
    /**
     * 获取电压等级描述
     */
    public String getVoltageLevelDescription() {
        int level = voltageLevel & 0xFF;
        switch (level) {
            case 0: return "低电关机";
            case 1: return "电量不足以打电话发短信";
            case 2: return "低电过低";
            case 3:
            case 4:
            case 5:
            case 6: return "正常使用 (等级" + level + ")";
            default: return "未知等级";
        }
    }
    
    /**
     * 获取GSM信号强度百分比
     */
    public int getGsmSignalPercentage() {
        return Math.min(100, Math.max(0, gsmSignalStrength & 0xFF));
    }
    
    /**
     * 获取语言类型
     */
    public LanguageType getLanguageType() {
        switch (language & 0xFF) {
            case 1: return LanguageType.CHINESE;
            case 2: return LanguageType.ENGLISH;
            default: return LanguageType.UNKNOWN;
        }
    }
    
    /**
     * 获取外部电压值（伏特）
     */
    public double getExternalVoltageValue() {
        return (externalVoltage & 0xFF) * 1.0; // 简化处理，实际可能需要转换公式
    }
    
    // Getter和Setter方法
    public byte getTerminalInfo() { return terminalInfo; }
    public void setTerminalInfo(byte terminalInfo) { this.terminalInfo = terminalInfo; }
    
    public byte getVoltageLevel() { return voltageLevel; }
    public void setVoltageLevel(byte voltageLevel) { this.voltageLevel = voltageLevel; }
    
    public byte getGsmSignalStrength() { return gsmSignalStrength; }
    public void setGsmSignalStrength(byte gsmSignalStrength) { this.gsmSignalStrength = gsmSignalStrength; }
    
    public byte getExternalVoltage() { return externalVoltage; }
    public void setExternalVoltage(byte externalVoltage) { this.externalVoltage = externalVoltage; }
    
    public byte getLanguage() { return language; }
    public void setLanguage(byte language) { this.language = language; }
    
    /**
     * 报警类型枚举
     */
    public enum AlarmType {
        NORMAL("正常"),
        VIBRATION("震动报警"),
        POWER_OFF("断电报警"),
        LOW_BATTERY("低电报警"),
        SOS("SOS报警"),
        DOOR_CLOSE("门关报警"),
        DOOR_OPEN("门开报警"),
        UNKNOWN("未知");
        
        private final String description;
        
        AlarmType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 语言类型枚举
     */
    public enum LanguageType {
        CHINESE("中文"),
        ENGLISH("英文"),
        UNKNOWN("未知");
        
        private final String description;
        
        LanguageType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Override
    public String toString() {
        return String.format("心跳包 - GPS定位: %s, 报警: %s, ACC: %s, GSM信号: %d%%, %s", 
                           isGpsPositioned() ? "是" : "否",
                           getAlarmType().getDescription(),
                           isAccOn() ? "开" : "关",
                           getGsmSignalPercentage(),
                           super.toString());
    }
}