package com.gt06.model;

import java.nio.ByteBuffer;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * GT06协议定位数据包 - JDK21版本
 * 使用现代Java特性优化的定位数据包实现
 * 
 * 协议号: 0x12
 * 
 * <AUTHOR>
 * @version 2.1.0
 * @since JDK21
 */
public final class LocationPacket extends ProtocolPacket {
    
    /** 定位数据包协议号 */
    public static final byte PROTOCOL_NUMBER = 0x12;
    
    /** 定位数据包长度 */
    private static final int LOCATION_DATA_LENGTH = 21;
    
    /** GPS信息 */
    private GpsInfo gpsInfo;
    
    /** LBS信息 */
    private LbsInfo lbsInfo;
    
    /** 状态信息 */
    private StatusInfo statusInfo;
    
    /** 电压信息 */
    private VoltageInfo voltageInfo;
    
    /** GSM信号强度 */
    private int gsmSignalStrength;
    
    /** 定位时间 */
    private Instant locationTime;
    
    /**
     * GPS信息记录类 - 使用JDK21的记录类特性
     */
    public record GpsInfo(
        double latitude,      // 纬度
        double longitude,     // 经度
        int speed,           // 速度 (km/h)
        int course,          // 航向角 (度)
        int altitude,        // 海拔高度 (米)
        int satelliteCount,  // 卫星数量
        boolean valid        // GPS数据是否有效
    ) {
        
        public GpsInfo {
            if (latitude < -90.0 || latitude > 90.0) {
                throw new IllegalArgumentException("纬度必须在-90到90度之间");
            }
            if (longitude < -180.0 || longitude > 180.0) {
                throw new IllegalArgumentException("经度必须在-180到180度之间");
            }
            if (speed < 0) {
                throw new IllegalArgumentException("速度不能为负数");
            }
            if (course < 0 || course >= 360) {
                throw new IllegalArgumentException("航向角必须在0到359度之间");
            }
            if (satelliteCount < 0) {
                throw new IllegalArgumentException("卫星数量不能为负数");
            }
        }
        
        /**
         * 检查位置是否有效（不在原点）
         */
        public boolean isLocationValid() {
            return valid && (latitude != 0.0 || longitude != 0.0);
        }
        
        /**
         * 获取GPS质量等级
         */
        public GpsQuality getQuality() {
            if (!valid) return GpsQuality.INVALID;
            if (satelliteCount >= 8) return GpsQuality.EXCELLENT;
            if (satelliteCount >= 6) return GpsQuality.GOOD;
            if (satelliteCount >= 4) return GpsQuality.FAIR;
            return GpsQuality.POOR;
        }
        
        /**
         * 计算与另一个GPS点的距离（米）
         */
        public double distanceTo(GpsInfo other) {
            if (!this.isLocationValid() || !other.isLocationValid()) {
                return -1.0; // 无效距离
            }
            
            double lat1Rad = Math.toRadians(this.latitude);
            double lon1Rad = Math.toRadians(this.longitude);
            double lat2Rad = Math.toRadians(other.latitude);
            double lon2Rad = Math.toRadians(other.longitude);
            
            double deltaLat = lat2Rad - lat1Rad;
            double deltaLon = lon2Rad - lon1Rad;
            
            double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                      Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                      Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
            
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            
            return 6371000 * c; // 地球半径 6371km
        }
        
        /**
         * 格式化GPS信息
         */
        public String formatLocation() {
            if (!isLocationValid()) {
                return "位置无效";
            }
            
            return """
                GPS位置: %.6f°, %.6f°
                速度: %d km/h
                航向: %d°
                海拔: %d m
                卫星数: %d
                质量: %s
                """.formatted(
                    latitude, longitude, speed, course, 
                    altitude, satelliteCount, getQuality().getDescription()
                );
        }
    }
    
    /**
     * LBS信息记录类
     */
    public record LbsInfo(
        int mcc,    // 移动国家代码
        int mnc,    // 移动网络代码
        int lac,    // 位置区域码
        int cellId, // 基站ID
        int rssi    // 信号强度
    ) {
        
        public LbsInfo {
            if (mcc < 0 || mcc > 999) {
                throw new IllegalArgumentException("MCC必须在0到999之间");
            }
            if (mnc < 0 || mnc > 999) {
                throw new IllegalArgumentException("MNC必须在0到999之间");
            }
            if (lac < 0) {
                throw new IllegalArgumentException("LAC不能为负数");
            }
            if (cellId < 0) {
                throw new IllegalArgumentException("基站ID不能为负数");
            }
        }
        
        /**
         * 检查LBS信息是否有效
         */
        public boolean isValid() {
            return mcc > 0 && mnc >= 0 && lac > 0 && cellId > 0;
        }
        
        /**
         * 获取信号强度等级
         */
        public SignalStrength getSignalStrength() {
            return switch (rssi) {
                case int r when r >= -70 -> SignalStrength.EXCELLENT;
                case int r when r >= -85 -> SignalStrength.GOOD;
                case int r when r >= -100 -> SignalStrength.FAIR;
                default -> SignalStrength.POOR;
            };
        }
        
        /**
         * 格式化LBS信息
         */
        public String formatLbs() {
            return """
                LBS信息:
                  MCC: %d
                  MNC: %d
                  LAC: %d
                  基站ID: %d
                  信号强度: %d dBm (%s)
                """.formatted(
                    mcc, mnc, lac, cellId, rssi, 
                    getSignalStrength().getDescription()
                );
        }
    }
    
    /**
     * 状态信息记录类
     */
    public record StatusInfo(
        boolean accOn,        // ACC开关状态
        boolean gpsTracking,  // GPS跟踪状态
        boolean charging,     // 充电状态
        boolean armed,        // 设防状态
        boolean moving        // 运动状态
    ) {
        
        /**
         * 获取状态描述
         */
        public String getStatusDescription() {
            return """
                设备状态:
                  ACC: %s
                  GPS跟踪: %s
                  充电: %s
                  设防: %s
                  运动: %s
                """.formatted(
                    accOn ? "开启" : "关闭",
                    gpsTracking ? "开启" : "关闭",
                    charging ? "充电中" : "未充电",
                    armed ? "已设防" : "未设防",
                    moving ? "运动中" : "静止"
                );
        }
        
        /**
         * 检查是否为正常工作状态
         */
        public boolean isNormalWorking() {
            return gpsTracking && !armed;
        }
    }
    
    /**
     * 电压信息记录类
     */
    public record VoltageInfo(
        double mainVoltage,    // 主电源电压 (V)
        double backupVoltage,  // 备用电池电压 (V)
        int batteryLevel       // 电池电量百分比
    ) {
        
        public VoltageInfo {
            if (mainVoltage < 0) {
                throw new IllegalArgumentException("主电源电压不能为负数");
            }
            if (backupVoltage < 0) {
                throw new IllegalArgumentException("备用电池电压不能为负数");
            }
            if (batteryLevel < 0 || batteryLevel > 100) {
                throw new IllegalArgumentException("电池电量必须在0到100之间");
            }
        }
        
        /**
         * 检查电压是否正常
         */
        public boolean isVoltageNormal() {
            return mainVoltage >= 11.0 && backupVoltage >= 3.0;
        }
        
        /**
         * 获取电池状态
         */
        public BatteryStatus getBatteryStatus() {
            return switch (batteryLevel) {
                case int level when level >= 80 -> BatteryStatus.HIGH;
                case int level when level >= 50 -> BatteryStatus.MEDIUM;
                case int level when level >= 20 -> BatteryStatus.LOW;
                default -> BatteryStatus.CRITICAL;
            };
        }
        
        /**
         * 格式化电压信息
         */
        public String formatVoltage() {
            return """
                电压信息:
                  主电源: %.2f V
                  备用电池: %.2f V
                  电池电量: %d%% (%s)
                  电压状态: %s
                """.formatted(
                    mainVoltage, backupVoltage, batteryLevel,
                    getBatteryStatus().getDescription(),
                    isVoltageNormal() ? "正常" : "异常"
                );
        }
    }
    
    /**
     * GPS质量枚举
     */
    public enum GpsQuality {
        INVALID("无效", "GPS信号无效"),
        POOR("差", "GPS信号较差，定位精度低"),
        FAIR("一般", "GPS信号一般，定位精度中等"),
        GOOD("良好", "GPS信号良好，定位精度较高"),
        EXCELLENT("优秀", "GPS信号优秀，定位精度很高");
        
        private final String description;
        private final String detail;
        
        GpsQuality(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() { return description; }
        public String getDetail() { return detail; }
    }
    
    /**
     * 信号强度枚举
     */
    public enum SignalStrength {
        POOR("差", "信号很弱"),
        FAIR("一般", "信号一般"),
        GOOD("良好", "信号良好"),
        EXCELLENT("优秀", "信号很强");
        
        private final String description;
        private final String detail;
        
        SignalStrength(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() { return description; }
        public String getDetail() { return detail; }
    }
    
    /**
     * 电池状态枚举
     */
    public enum BatteryStatus {
        CRITICAL("严重不足", "电池电量严重不足，需要立即充电"),
        LOW("不足", "电池电量不足，建议充电"),
        MEDIUM("中等", "电池电量中等"),
        HIGH("充足", "电池电量充足");
        
        private final String description;
        private final String detail;
        
        BatteryStatus(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() { return description; }
        public String getDetail() { return detail; }
    }
    
    /**
     * 默认构造函数
     */
    public LocationPacket() {
        super(PROTOCOL_NUMBER, 1);
        this.locationTime = Instant.now();
    }
    
    /**
     * 构造函数
     * 
     * @param serialNumber 序列号
     */
    public LocationPacket(int serialNumber) {
        super(PROTOCOL_NUMBER, serialNumber);
        this.locationTime = Instant.now();
    }
    
    /**
     * 完整构造函数
     * 
     * @param gpsInfo GPS信息
     * @param lbsInfo LBS信息
     * @param statusInfo 状态信息
     * @param voltageInfo 电压信息
     * @param serialNumber 序列号
     */
    public LocationPacket(GpsInfo gpsInfo, LbsInfo lbsInfo, StatusInfo statusInfo, 
                         VoltageInfo voltageInfo, int serialNumber) {
        super(PROTOCOL_NUMBER, serialNumber);
        this.gpsInfo = gpsInfo;
        this.lbsInfo = lbsInfo;
        this.statusInfo = statusInfo;
        this.voltageInfo = voltageInfo;
        this.locationTime = Instant.now();
    }
    
    @Override
    public byte[] getInfoContent() {
        ByteBuffer buffer = ByteBuffer.allocate(LOCATION_DATA_LENGTH);
        
        // GPS信息 (12字节)
        if (gpsInfo != null) {
            buffer.putInt((int) (gpsInfo.latitude() * 1800000)); // 纬度
            buffer.putInt((int) (gpsInfo.longitude() * 1800000)); // 经度
            buffer.put((byte) gpsInfo.speed()); // 速度
            buffer.putShort((short) gpsInfo.course()); // 航向
            buffer.put((byte) gpsInfo.satelliteCount()); // 卫星数
        } else {
            buffer.position(buffer.position() + 12);
        }
        
        // LBS信息 (5字节)
        if (lbsInfo != null) {
            buffer.putShort((short) lbsInfo.mcc()); // MCC
            buffer.put((byte) lbsInfo.mnc()); // MNC
            buffer.putShort((short) lbsInfo.lac()); // LAC
        } else {
            buffer.position(buffer.position() + 5);
        }
        
        // 状态信息 (1字节)
        byte statusByte = 0;
        if (statusInfo != null) {
            if (statusInfo.accOn()) statusByte |= 0x01;
            if (statusInfo.gpsTracking()) statusByte |= 0x02;
            if (statusInfo.charging()) statusByte |= 0x04;
            if (statusInfo.armed()) statusByte |= 0x08;
            if (statusInfo.moving()) statusByte |= 0x10;
        }
        buffer.put(statusByte);
        
        // 电压信息 (3字节)
        if (voltageInfo != null) {
            buffer.put((byte) (voltageInfo.mainVoltage() * 10)); // 主电压
            buffer.put((byte) (voltageInfo.backupVoltage() * 10)); // 备用电压
            buffer.put((byte) voltageInfo.batteryLevel()); // 电池电量
        } else {
            buffer.position(buffer.position() + 3);
        }
        
        return buffer.array();
    }
    
    @Override
    public void parseFromBytes(byte[] data, int offset) {
        if (data == null || data.length < offset + LOCATION_DATA_LENGTH) {
            throw new IllegalArgumentException("数据长度不足，无法解析定位数据包");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data, offset, LOCATION_DATA_LENGTH);
        
        // 解析GPS信息
        int latInt = buffer.getInt();
        int lonInt = buffer.getInt();
        int speed = buffer.get() & 0xFF;
        int course = buffer.getShort() & 0xFFFF;
        int satelliteCount = buffer.get() & 0xFF;
        
        double latitude = latInt / 1800000.0;
        double longitude = lonInt / 1800000.0;
        boolean gpsValid = latitude != 0.0 || longitude != 0.0;
        
        this.gpsInfo = new GpsInfo(latitude, longitude, speed, course, 0, satelliteCount, gpsValid);
        
        // 解析LBS信息
        int mcc = buffer.getShort() & 0xFFFF;
        int mnc = buffer.get() & 0xFF;
        int lac = buffer.getShort() & 0xFFFF;
        
        this.lbsInfo = new LbsInfo(mcc, mnc, lac, 0, -80); // 默认信号强度
        
        // 解析状态信息
        byte statusByte = buffer.get();
        boolean accOn = (statusByte & 0x01) != 0;
        boolean gpsTracking = (statusByte & 0x02) != 0;
        boolean charging = (statusByte & 0x04) != 0;
        boolean armed = (statusByte & 0x08) != 0;
        boolean moving = (statusByte & 0x10) != 0;
        
        this.statusInfo = new StatusInfo(accOn, gpsTracking, charging, armed, moving);
        
        // 解析电压信息
        double mainVoltage = (buffer.get() & 0xFF) / 10.0;
        double backupVoltage = (buffer.get() & 0xFF) / 10.0;
        int batteryLevel = buffer.get() & 0xFF;
        
        this.voltageInfo = new VoltageInfo(mainVoltage, backupVoltage, batteryLevel);
        
        this.locationTime = Instant.now();
    }
    
    @Override
    public ValidationResult validate() {
        // 调用父类验证
        ValidationResult parentResult = super.validate();
        if (!parentResult.valid()) {
            return parentResult;
        }
        
        // 验证GPS信息
        if (gpsInfo != null && !gpsInfo.isLocationValid()) {
            return ValidationResult.failure("GPS位置数据无效");
        }
        
        // 验证LBS信息
        if (lbsInfo != null && !lbsInfo.isValid()) {
            return ValidationResult.failure("LBS信息无效");
        }
        
        // 验证电压信息
        if (voltageInfo != null && !voltageInfo.isVoltageNormal()) {
            return ValidationResult.failure("电压异常");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 检查是否为紧急情况
     */
    public boolean isEmergency() {
        if (voltageInfo != null && voltageInfo.getBatteryStatus() == BatteryStatus.CRITICAL) {
            return true;
        }
        
        if (statusInfo != null && statusInfo.armed() && statusInfo.moving()) {
            return true; // 设防状态下移动
        }
        
        return false;
    }
    
    /**
     * 获取位置质量评分 (0-100)
     */
    public int getLocationQualityScore() {
        if (gpsInfo == null || !gpsInfo.isLocationValid()) {
            return 0;
        }
        
        int score = switch (gpsInfo.getQuality()) {
            case EXCELLENT -> 90;
            case GOOD -> 75;
            case FAIR -> 50;
            case POOR -> 25;
            case INVALID -> 0;
        };
        
        // 根据LBS信息调整分数
        if (lbsInfo != null && lbsInfo.isValid()) {
            score += 10;
        }
        
        return Math.min(score, 100);
    }
    
    // Getter和Setter方法
    public GpsInfo getGpsInfo() {
        return gpsInfo;
    }
    
    public void setGpsInfo(GpsInfo gpsInfo) {
        this.gpsInfo = gpsInfo;
    }
    
    public LbsInfo getLbsInfo() {
        return lbsInfo;
    }
    
    public void setLbsInfo(LbsInfo lbsInfo) {
        this.lbsInfo = lbsInfo;
    }
    
    public StatusInfo getStatusInfo() {
        return statusInfo;
    }
    
    public void setStatusInfo(StatusInfo statusInfo) {
        this.statusInfo = statusInfo;
    }
    
    public VoltageInfo getVoltageInfo() {
        return voltageInfo;
    }
    
    public void setVoltageInfo(VoltageInfo voltageInfo) {
        this.voltageInfo = voltageInfo;
    }
    
    public int getGsmSignalStrength() {
        return gsmSignalStrength;
    }
    
    public void setGsmSignalStrength(int gsmSignalStrength) {
        this.gsmSignalStrength = gsmSignalStrength;
    }
    
    public Instant getLocationTime() {
        return locationTime;
    }
    
    // 便利方法
    public double getLatitude() {
        return gpsInfo != null ? gpsInfo.latitude() : 0.0;
    }
    
    public void setLatitude(double latitude) {
        if (gpsInfo != null) {
            this.gpsInfo = new GpsInfo(latitude, gpsInfo.longitude(), gpsInfo.speed(), 
                                     gpsInfo.course(), gpsInfo.altitude(), 
                                     gpsInfo.satelliteCount(), gpsInfo.valid());
        } else {
            this.gpsInfo = new GpsInfo(latitude, 0.0, 0, 0, 0, 0, true);
        }
    }
    
    public double getLongitude() {
        return gpsInfo != null ? gpsInfo.longitude() : 0.0;
    }
    
    public void setLongitude(double longitude) {
        if (gpsInfo != null) {
            this.gpsInfo = new GpsInfo(gpsInfo.latitude(), longitude, gpsInfo.speed(), 
                                     gpsInfo.course(), gpsInfo.altitude(), 
                                     gpsInfo.satelliteCount(), gpsInfo.valid());
        } else {
            this.gpsInfo = new GpsInfo(0.0, longitude, 0, 0, 0, 0, true);
        }
    }
    
    @Override
    public String toString() {
        return """
            定位数据包信息:
              %s
              定位时间: %s
              位置质量评分: %d/100
              紧急状态: %s
              
              %s
              
              %s
              
              %s
              
              %s
            """.formatted(
                super.toString().replace("协议包信息:", "").trim(),
                locationTime,
                getLocationQualityScore(),
                isEmergency() ? "是" : "否",
                gpsInfo != null ? gpsInfo.formatLocation() : "GPS信息未设置",
                lbsInfo != null ? lbsInfo.formatLbs() : "LBS信息未设置",
                statusInfo != null ? statusInfo.getStatusDescription() : "状态信息未设置",
                voltageInfo != null ? voltageInfo.formatVoltage() : "电压信息未设置"
            );
    }
}