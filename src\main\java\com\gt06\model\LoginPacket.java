package com.gt06.model;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Objects;

/**
 * GT06协议登录包 - JDK21版本
 * 使用现代Java特性优化的登录包实现
 * 
 * 协议号: 0x01
 * 
 * <AUTHOR>
 * @version 2.1.0
 * @since JDK21
 */
public final class LoginPacket extends ProtocolPacket {
    
    /** 登录包协议号 */
    public static final byte PROTOCOL_NUMBER = 0x01;
    
    /** 终端ID长度 */
    private static final int TERMINAL_ID_LENGTH = 8;
    
    /** 终端类型长度 */
    private static final int TERMINAL_TYPE_LENGTH = 2;
    
    /** 时区语言长度 */
    private static final int TIMEZONE_LANGUAGE_LENGTH = 2;
    
    /** 终端ID */
    private String terminalId;
    
    /** 终端类型 */
    private String terminalType;
    
    /** 时区语言 */
    private String timezoneLanguage;
    
    /** 登录时间 */
    private final Instant loginTime;
    
    /** 登录状态 */
    private LoginStatus status;
    
    /**
     * 登录状态枚举 - 使用JDK21的现代枚举特性
     */
    public enum LoginStatus {
        PENDING("待处理", "登录请求已接收，等待处理"),
        SUCCESS("成功", "登录成功"),
        FAILED("失败", "登录失败"),
        TIMEOUT("超时", "登录超时"),
        REJECTED("拒绝", "登录被拒绝");
        
        private final String description;
        private final String detail;
        
        LoginStatus(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getDetail() {
            return detail;
        }
        
        /**
         * 检查是否为成功状态
         */
        public boolean isSuccess() {
            return this == SUCCESS;
        }
        
        /**
         * 检查是否为失败状态
         */
        public boolean isFailure() {
            return this == FAILED || this == TIMEOUT || this == REJECTED;
        }
    }
    
    /**
     * 默认构造函数
     */
    public LoginPacket() {
        super(PROTOCOL_NUMBER, 1);
        this.loginTime = Instant.now();
        this.status = LoginStatus.PENDING;
    }
    
    /**
     * 构造函数
     * 
     * @param terminalId 终端ID
     * @param serialNumber 序列号
     */
    public LoginPacket(String terminalId, int serialNumber) {
        super(PROTOCOL_NUMBER, serialNumber);
        this.terminalId = Objects.requireNonNull(terminalId, "终端ID不能为空");
        this.loginTime = Instant.now();
        this.status = LoginStatus.PENDING;
    }
    
    /**
     * 完整构造函数
     * 
     * @param terminalId 终端ID
     * @param terminalType 终端类型
     * @param timezoneLanguage 时区语言
     * @param serialNumber 序列号
     */
    public LoginPacket(String terminalId, String terminalType, 
                      String timezoneLanguage, int serialNumber) {
        super(PROTOCOL_NUMBER, serialNumber, terminalId);
        this.terminalId = Objects.requireNonNull(terminalId, "终端ID不能为空");
        this.terminalType = Objects.requireNonNull(terminalType, "终端类型不能为空");
        this.timezoneLanguage = Objects.requireNonNull(timezoneLanguage, "时区语言不能为空");
        this.loginTime = Instant.now();
        this.status = LoginStatus.PENDING;
    }
    
    @Override
    public byte[] getInfoContent() {
        ByteBuffer buffer = ByteBuffer.allocate(TERMINAL_ID_LENGTH + 
                                              TERMINAL_TYPE_LENGTH + 
                                              TIMEZONE_LANGUAGE_LENGTH);
        
        // 终端ID (8字节)
        byte[] terminalIdBytes = padOrTruncate(
            terminalId != null ? terminalId : "", 
            TERMINAL_ID_LENGTH
        );
        buffer.put(terminalIdBytes);
        
        // 终端类型 (2字节)
        byte[] terminalTypeBytes = padOrTruncate(
            terminalType != null ? terminalType : "GT", 
            TERMINAL_TYPE_LENGTH
        );
        buffer.put(terminalTypeBytes);
        
        // 时区语言 (2字节)
        byte[] timezoneLanguageBytes = padOrTruncate(
            timezoneLanguage != null ? timezoneLanguage : "CN", 
            TIMEZONE_LANGUAGE_LENGTH
        );
        buffer.put(timezoneLanguageBytes);
        
        return buffer.array();
    }
    
    @Override
    public void parseFromBytes(byte[] data, int offset) {
        if (data == null || data.length < offset + TERMINAL_ID_LENGTH + 
                                         TERMINAL_TYPE_LENGTH + 
                                         TIMEZONE_LANGUAGE_LENGTH) {
            throw new IllegalArgumentException("数据长度不足，无法解析登录包");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data, offset, 
                                          TERMINAL_ID_LENGTH + 
                                          TERMINAL_TYPE_LENGTH + 
                                          TIMEZONE_LANGUAGE_LENGTH);
        
        // 解析终端ID
        byte[] terminalIdBytes = new byte[TERMINAL_ID_LENGTH];
        buffer.get(terminalIdBytes);
        this.terminalId = new String(terminalIdBytes, StandardCharsets.UTF_8).trim();
        
        // 解析终端类型
        byte[] terminalTypeBytes = new byte[TERMINAL_TYPE_LENGTH];
        buffer.get(terminalTypeBytes);
        this.terminalType = new String(terminalTypeBytes, StandardCharsets.UTF_8).trim();
        
        // 解析时区语言
        byte[] timezoneLanguageBytes = new byte[TIMEZONE_LANGUAGE_LENGTH];
        buffer.get(timezoneLanguageBytes);
        this.timezoneLanguage = new String(timezoneLanguageBytes, StandardCharsets.UTF_8).trim();
        
        // 设置设备ID为终端ID
        this.setDeviceId(this.terminalId);
    }
    
    @Override
    public ValidationResult validate() {
        // 调用父类验证
        ValidationResult parentResult = super.validate();
        if (!parentResult.valid()) {
            return parentResult;
        }
        
        // 验证终端ID
        if (terminalId == null || terminalId.trim().isEmpty()) {
            return ValidationResult.failure("终端ID不能为空");
        }
        
        if (terminalId.length() > TERMINAL_ID_LENGTH) {
            return ValidationResult.failure("终端ID长度不能超过" + TERMINAL_ID_LENGTH + "字节");
        }
        
        // 验证终端类型
        if (terminalType != null && terminalType.length() > TERMINAL_TYPE_LENGTH) {
            return ValidationResult.failure("终端类型长度不能超过" + TERMINAL_TYPE_LENGTH + "字节");
        }
        
        // 验证时区语言
        if (timezoneLanguage != null && timezoneLanguage.length() > TIMEZONE_LANGUAGE_LENGTH) {
            return ValidationResult.failure("时区语言长度不能超过" + TIMEZONE_LANGUAGE_LENGTH + "字节");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 创建登录响应包
     * 
     * @param success 是否成功
     * @return 响应包数据
     */
    public byte[] createResponse(boolean success) {
        ByteBuffer buffer = ByteBuffer.allocate(5);
        
        // 协议号
        buffer.put((byte) 0x01);
        
        // 响应结果 (0x00: 成功, 0x01: 失败)
        buffer.put(success ? (byte) 0x00 : (byte) 0x01);
        
        // 序列号 (2字节)
        buffer.putShort((short) getSerialNumber());
        
        return buffer.array();
    }
    
    /**
     * 检查登录是否超时
     * 
     * @param timeoutSeconds 超时时间（秒）
     * @return 是否超时
     */
    public boolean isLoginTimeout(long timeoutSeconds) {
        return Instant.now().isAfter(loginTime.plusSeconds(timeoutSeconds));
    }
    
    /**
     * 获取登录持续时间（毫秒）
     * 
     * @return 登录持续时间
     */
    public long getLoginDurationMillis() {
        return Instant.now().toEpochMilli() - loginTime.toEpochMilli();
    }
    
    /**
     * 字符串填充或截断到指定长度
     * 
     * @param str 原字符串
     * @param length 目标长度
     * @return 处理后的字节数组
     */
    private byte[] padOrTruncate(String str, int length) {
        byte[] result = new byte[length];
        byte[] strBytes = str.getBytes(StandardCharsets.UTF_8);
        
        int copyLength = Math.min(strBytes.length, length);
        System.arraycopy(strBytes, 0, result, 0, copyLength);
        
        // 剩余位置填充0
        for (int i = copyLength; i < length; i++) {
            result[i] = 0;
        }
        
        return result;
    }
    
    // Getter和Setter方法
    public String getTerminalId() {
        return terminalId;
    }
    
    public void setTerminalId(String terminalId) {
        this.terminalId = Objects.requireNonNull(terminalId, "终端ID不能为空").trim();
        if (this.terminalId.isEmpty()) {
            throw new IllegalArgumentException("终端ID不能为空字符串");
        }
        // 同时设置设备ID
        this.setDeviceId(this.terminalId);
    }
    
    public String getTerminalType() {
        return terminalType;
    }
    
    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType != null ? terminalType.trim() : null;
    }
    
    public String getTimezoneLanguage() {
        return timezoneLanguage;
    }
    
    public void setTimezoneLanguage(String timezoneLanguage) {
        this.timezoneLanguage = timezoneLanguage != null ? timezoneLanguage.trim() : null;
    }
    
    public Instant getLoginTime() {
        return loginTime;
    }
    
    public LoginStatus getStatus() {
        return status;
    }
    
    public void setStatus(LoginStatus status) {
        this.status = Objects.requireNonNull(status, "登录状态不能为空");
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!super.equals(obj)) return false;
        if (!(obj instanceof LoginPacket that)) return false;
        
        return Objects.equals(terminalId, that.terminalId) &&
               Objects.equals(terminalType, that.terminalType) &&
               Objects.equals(timezoneLanguage, that.timezoneLanguage);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), terminalId, terminalType, timezoneLanguage);
    }
    
    @Override
    public String toString() {
        return """
            登录包信息:
              %s
              终端ID: %s
              终端类型: %s
              时区语言: %s
              登录时间: %s
              登录状态: %s (%s)
              登录持续时间: %d ms
            """.formatted(
                super.toString().replace("协议包信息:", "").trim(),
                terminalId != null ? terminalId : "未设置",
                terminalType != null ? terminalType : "未设置",
                timezoneLanguage != null ? timezoneLanguage : "未设置",
                loginTime,
                status.getDescription(),
                status.getDetail(),
                getLoginDurationMillis()
            );
    }
    
    /**
     * 登录包构建器 - 使用建造者模式
     */
    public static class Builder {
        private String terminalId;
        private String terminalType = "GT";
        private String timezoneLanguage = "CN";
        private int serialNumber = 1;
        
        public Builder terminalId(String terminalId) {
            this.terminalId = terminalId;
            return this;
        }
        
        public Builder terminalType(String terminalType) {
            this.terminalType = terminalType;
            return this;
        }
        
        public Builder timezoneLanguage(String timezoneLanguage) {
            this.timezoneLanguage = timezoneLanguage;
            return this;
        }
        
        public Builder serialNumber(int serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }
        
        public LoginPacket build() {
            return new LoginPacket(terminalId, terminalType, timezoneLanguage, serialNumber);
        }
    }
    
    /**
     * 创建构建器
     * 
     * @return 登录包构建器
     */
    public static Builder builder() {
        return new Builder();
    }
}