package com.gt06.model;

import java.time.Instant;
import java.util.Objects;

/**
 * GT06协议包基础类 - JDK21版本
 * 使用现代Java特性优化的协议包基础类
 * 
 * <AUTHOR>
 * @version 2.1.0
 * @since JDK21
 */
public abstract sealed class ProtocolPacket 
    permits LoginPacket, LocationPacket, HeartbeatPacket, AlarmPacket, CommandResponsePacket {
    
    /** 起始位 - 固定值0x7878 */
    public static final byte[] START_BIT = {0x78, 0x78};
    
    /** 停止位 - 固定值0x0D0A */
    public static final byte[] STOP_BIT = {0x0D, 0x0A};
    
    /** 包长度 */
    protected int length;
    
    /** 协议号 */
    protected byte protocolNumber;
    
    /** 信息序列号 */
    protected int serialNumber;
    
    /** 错误校验 */
    protected int crc;
    
    /** 创建时间戳 */
    protected final Instant timestamp;
    
    /** 设备ID */
    protected String deviceId;
    
    /**
     * 默认构造函数
     */
    protected ProtocolPacket() {
        this.timestamp = Instant.now();
    }
    
    /**
     * 构造函数
     * 
     * @param protocolNumber 协议号
     * @param serialNumber 序列号
     */
    protected ProtocolPacket(byte protocolNumber, int serialNumber) {
        this.protocolNumber = protocolNumber;
        this.serialNumber = serialNumber;
        this.timestamp = Instant.now();
    }
    
    /**
     * 完整构造函数
     * 
     * @param protocolNumber 协议号
     * @param serialNumber 序列号
     * @param deviceId 设备ID
     */
    protected ProtocolPacket(byte protocolNumber, int serialNumber, String deviceId) {
        this.protocolNumber = protocolNumber;
        this.serialNumber = serialNumber;
        this.deviceId = Objects.requireNonNull(deviceId, "设备ID不能为空");
        this.timestamp = Instant.now();
    }
    
    /**
     * 获取协议包的信息内容字节数组
     * 子类需要实现此方法
     * 
     * @return 信息内容字节数组
     */
    public abstract byte[] getInfoContent();
    
    /**
     * 从字节数组解析协议包
     * 子类需要实现此方法
     * 
     * @param data 数据字节数组
     * @param offset 偏移量
     */
    public abstract void parseFromBytes(byte[] data, int offset);
    
    /**
     * 验证协议包数据的有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        if (length <= 0) {
            return new ValidationResult(false, "协议包长度无效");
        }
        
        if (serialNumber < 0) {
            return new ValidationResult(false, "序列号无效");
        }
        
        if (deviceId != null && deviceId.trim().isEmpty()) {
            return new ValidationResult(false, "设备ID不能为空");
        }
        
        return new ValidationResult(true, "验证通过");
    }
    
    /**
     * 获取协议包类型描述
     * 
     * @return 协议包类型描述
     */
    public String getPacketTypeDescription() {
        return switch (protocolNumber) {
            case 0x01 -> "登录包";
            case 0x12 -> "定位数据包";
            case 0x13 -> "心跳包";
            case 0x16 -> "报警包";
            case (byte) 0x80 -> "指令响应包";
            default -> "未知协议包(0x" + String.format("%02X", protocolNumber) + ")";
        };
    }
    
    /**
     * 检查协议包是否过期
     * 
     * @param timeoutSeconds 超时时间（秒）
     * @return 是否过期
     */
    public boolean isExpired(long timeoutSeconds) {
        return Instant.now().isAfter(timestamp.plusSeconds(timeoutSeconds));
    }
    
    /**
     * 获取协议包年龄（毫秒）
     * 
     * @return 协议包年龄
     */
    public long getAgeInMillis() {
        return Instant.now().toEpochMilli() - timestamp.toEpochMilli();
    }
    
    // Getter和Setter方法
    public int getLength() {
        return length;
    }
    
    public void setLength(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("协议包长度必须大于0");
        }
        this.length = length;
    }
    
    public byte getProtocolNumber() {
        return protocolNumber;
    }
    
    public void setProtocolNumber(byte protocolNumber) {
        this.protocolNumber = protocolNumber;
    }
    
    public int getSerialNumber() {
        return serialNumber;
    }
    
    public void setSerialNumber(int serialNumber) {
        if (serialNumber < 0) {
            throw new IllegalArgumentException("序列号不能为负数");
        }
        this.serialNumber = serialNumber;
    }
    
    public int getCrc() {
        return crc;
    }
    
    public void setCrc(int crc) {
        this.crc = crc;
    }
    
    public Instant getTimestamp() {
        return timestamp;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = Objects.requireNonNull(deviceId, "设备ID不能为空").trim();
        if (this.deviceId.isEmpty()) {
            throw new IllegalArgumentException("设备ID不能为空字符串");
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ProtocolPacket that = (ProtocolPacket) obj;
        return protocolNumber == that.protocolNumber &&
               serialNumber == that.serialNumber &&
               Objects.equals(deviceId, that.deviceId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(protocolNumber, serialNumber, deviceId);
    }
    
    @Override
    public String toString() {
        return """
            协议包信息:
              类型: %s
              协议号: 0x%02X
              序列号: %d
              长度: %d
              设备ID: %s
              创建时间: %s
              年龄: %d ms
            """.formatted(
                getPacketTypeDescription(),
                protocolNumber,
                serialNumber,
                length,
                deviceId != null ? deviceId : "未设置",
                timestamp,
                getAgeInMillis()
            );
    }
    
    /**
     * 验证结果记录类 - 使用JDK21的记录类特性
     */
    public record ValidationResult(boolean valid, String message) {
        
        public ValidationResult {
            Objects.requireNonNull(message, "验证消息不能为空");
        }
        
        /**
         * 创建成功的验证结果
         */
        public static ValidationResult success() {
            return new ValidationResult(true, "验证通过");
        }
        
        /**
         * 创建失败的验证结果
         */
        public static ValidationResult failure(String message) {
            return new ValidationResult(false, message);
        }
        
        /**
         * 如果验证失败，抛出异常
         */
        public void throwIfInvalid() {
            if (!valid) {
                throw new IllegalStateException("协议包验证失败: " + message);
            }
        }
    }
    
    /**
     * 协议包统计信息记录类
     */
    public record PacketStats(
        String packetType,
        int totalCount,
        long averageProcessingTime,
        long minProcessingTime,
        long maxProcessingTime,
        double successRate
    ) {
        
        public PacketStats {
            if (totalCount < 0) {
                throw new IllegalArgumentException("总数不能为负数");
            }
            if (successRate < 0.0 || successRate > 1.0) {
                throw new IllegalArgumentException("成功率必须在0.0到1.0之间");
            }
        }
        
        /**
         * 格式化统计信息
         */
        public String formatStats() {
            return """
                协议包统计:
                  类型: %s
                  总数: %d
                  平均处理时间: %d ms
                  最小处理时间: %d ms
                  最大处理时间: %d ms
                  成功率: %.2f%%
                """.formatted(
                    packetType,
                    totalCount,
                    averageProcessingTime,
                    minProcessingTime,
                    maxProcessingTime,
                    successRate * 100
                );
        }
    }
}