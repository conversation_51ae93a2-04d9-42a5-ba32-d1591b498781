package com.gt06.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 服务器性能指标监控
 * 收集和统计服务器运行指标
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ServerMetrics {
    
    private static final Logger logger = LoggerFactory.getLogger(ServerMetrics.class);
    
    /** 单例实例 */
    private static volatile ServerMetrics instance;
    
    /** 总连接数 */
    private final AtomicLong totalConnections = new AtomicLong(0);
    
    /** 当前活跃连接数 */
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    
    /** 接收的数据包总数 */
    private final AtomicLong totalPacketsReceived = new AtomicLong(0);
    
    /** 发送的数据包总数 */
    private final AtomicLong totalPacketsSent = new AtomicLong(0);
    
    /** 协议包类型统计 */
    private final ConcurrentHashMap<Byte, AtomicLong> packetTypeStats = new ConcurrentHashMap<>();
    
    /** 错误统计 */
    private final AtomicLong crcErrors = new AtomicLong(0);
    private final AtomicLong decodeErrors = new AtomicLong(0);
    private final AtomicLong connectionErrors = new AtomicLong(0);
    
    /** 性能统计 */
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    
    /** 设备统计 */
    private final ConcurrentHashMap<String, DeviceMetrics> deviceMetrics = new ConcurrentHashMap<>();
    
    /** 定时任务执行器 */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    /** 服务启动时间 */
    private final long startTime = System.currentTimeMillis();
    
    private ServerMetrics() {
        // 启动定时统计任务
        startPeriodicReporting();
    }
    
    /**
     * 获取单例实例
     */
    public static ServerMetrics getInstance() {
        if (instance == null) {
            synchronized (ServerMetrics.class) {
                if (instance == null) {
                    instance = new ServerMetrics();
                }
            }
        }
        return instance;
    }
    
    /**
     * 记录新连接
     */
    public void recordConnection() {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
    }
    
    /**
     * 记录连接断开
     */
    public void recordDisconnection() {
        activeConnections.decrementAndGet();
    }
    
    /**
     * 记录接收到的数据包
     */
    public void recordPacketReceived(byte protocolNumber) {
        totalPacketsReceived.incrementAndGet();
        packetTypeStats.computeIfAbsent(protocolNumber, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * 记录发送的数据包
     */
    public void recordPacketSent() {
        totalPacketsSent.incrementAndGet();
    }
    
    /**
     * 记录CRC错误
     */
    public void recordCrcError() {
        crcErrors.incrementAndGet();
    }
    
    /**
     * 记录解码错误
     */
    public void recordDecodeError() {
        decodeErrors.incrementAndGet();
    }
    
    /**
     * 记录连接错误
     */
    public void recordConnectionError() {
        connectionErrors.incrementAndGet();
    }
    
    /**
     * 记录处理时间
     */
    public void recordProcessingTime(long processingTime) {
        totalProcessingTime.addAndGet(processingTime);
        
        // 更新最大处理时间
        long currentMax = maxProcessingTime.get();
        while (processingTime > currentMax) {
            if (maxProcessingTime.compareAndSet(currentMax, processingTime)) {
                break;
            }
            currentMax = maxProcessingTime.get();
        }
    }
    
    /**
     * 记录设备活动
     */
    public void recordDeviceActivity(String imei, byte protocolNumber) {
        deviceMetrics.computeIfAbsent(imei, k -> new DeviceMetrics(imei))
                    .recordActivity(protocolNumber);
    }
    
    /**
     * 获取服务器运行时间（毫秒）
     */
    public long getUptime() {
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * 获取平均处理时间
     */
    public double getAverageProcessingTime() {
        long totalPackets = totalPacketsReceived.get();
        if (totalPackets == 0) {
            return 0.0;
        }
        return (double) totalProcessingTime.get() / totalPackets;
    }
    
    /**
     * 获取错误率
     */
    public double getErrorRate() {
        long totalPackets = totalPacketsReceived.get();
        if (totalPackets == 0) {
            return 0.0;
        }
        long totalErrors = crcErrors.get() + decodeErrors.get();
        return (double) totalErrors / totalPackets * 100;
    }
    
    /**
     * 获取当前QPS（每秒查询数）
     */
    public double getCurrentQPS() {
        long uptime = getUptime();
        if (uptime == 0) {
            return 0.0;
        }
        return (double) totalPacketsReceived.get() / (uptime / 1000.0);
    }
    
    /**
     * 启动定期报告任务
     */
    private void startPeriodicReporting() {
        // 每分钟输出统计信息
        scheduler.scheduleAtFixedRate(this::logMetrics, 1, 1, TimeUnit.MINUTES);
        
        // 每小时清理过期设备指标
        scheduler.scheduleAtFixedRate(this::cleanupDeviceMetrics, 1, 1, TimeUnit.HOURS);
    }
    
    /**
     * 输出指标日志
     */
    private void logMetrics() {
        long uptime = getUptime();
        logger.info("=== 服务器性能指标 ===");
        logger.info("运行时间: {} 小时", uptime / (1000 * 60 * 60));
        logger.info("总连接数: {}, 当前活跃连接: {}", totalConnections.get(), activeConnections.get());
        logger.info("接收数据包: {}, 发送数据包: {}", totalPacketsReceived.get(), totalPacketsSent.get());
        logger.info("当前QPS: {:.2f}", getCurrentQPS());
        logger.info("平均处理时间: {:.2f}ms, 最大处理时间: {}ms", 
                   getAverageProcessingTime(), maxProcessingTime.get());
        logger.info("错误率: {:.2f}% (CRC错误: {}, 解码错误: {}, 连接错误: {})", 
                   getErrorRate(), crcErrors.get(), decodeErrors.get(), connectionErrors.get());
        logger.info("在线设备数: {}", deviceMetrics.size());
        
        // 输出协议包类型统计
        if (!packetTypeStats.isEmpty()) {
            logger.info("协议包类型统计:");
            packetTypeStats.forEach((type, count) -> 
                logger.info("  0x{}: {} 个", String.format("%02X", type), count.get()));
        }
    }
    
    /**
     * 清理过期设备指标
     */
    private void cleanupDeviceMetrics() {
        long now = System.currentTimeMillis();
        deviceMetrics.entrySet().removeIf(entry -> {
            DeviceMetrics metrics = entry.getValue();
            return now - metrics.getLastActivity() > TimeUnit.HOURS.toMillis(24); // 24小时未活动
        });
        logger.debug("清理过期设备指标，当前设备数: {}", deviceMetrics.size());
    }
    
    /**
     * 获取详细指标信息
     */
    public MetricsSnapshot getSnapshot() {
        return new MetricsSnapshot(
            totalConnections.get(),
            activeConnections.get(),
            totalPacketsReceived.get(),
            totalPacketsSent.get(),
            crcErrors.get(),
            decodeErrors.get(),
            connectionErrors.get(),
            getAverageProcessingTime(),
            maxProcessingTime.get(),
            getCurrentQPS(),
            getErrorRate(),
            getUptime(),
            deviceMetrics.size(),
            new ConcurrentHashMap<>(packetTypeStats)
        );
    }
    
    /**
     * 关闭监控服务
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    // Getter方法
    public long getTotalConnections() { return totalConnections.get(); }
    public int getActiveConnections() { return activeConnections.get(); }
    public long getTotalPacketsReceived() { return totalPacketsReceived.get(); }
    public long getTotalPacketsSent() { return totalPacketsSent.get(); }
    public long getCrcErrors() { return crcErrors.get(); }
    public long getDecodeErrors() { return decodeErrors.get(); }
    public long getConnectionErrors() { return connectionErrors.get(); }
    public long getMaxProcessingTime() { return maxProcessingTime.get(); }
    
    /**
     * 设备指标类
     */
    public static class DeviceMetrics {
        private final String imei;
        private final AtomicLong totalPackets = new AtomicLong(0);
        private final ConcurrentHashMap<Byte, AtomicLong> packetTypes = new ConcurrentHashMap<>();
        private volatile long lastActivity = System.currentTimeMillis();
        private volatile long firstActivity = System.currentTimeMillis();
        
        public DeviceMetrics(String imei) {
            this.imei = imei;
        }
        
        public void recordActivity(byte protocolNumber) {
            totalPackets.incrementAndGet();
            packetTypes.computeIfAbsent(protocolNumber, k -> new AtomicLong(0)).incrementAndGet();
            lastActivity = System.currentTimeMillis();
        }
        
        public String getImei() { return imei; }
        public long getTotalPackets() { return totalPackets.get(); }
        public long getLastActivity() { return lastActivity; }
        public long getFirstActivity() { return firstActivity; }
        public ConcurrentHashMap<Byte, AtomicLong> getPacketTypes() { return packetTypes; }
    }
    
    /**
     * 指标快照类
     */
    public static class MetricsSnapshot {
        private final long totalConnections;
        private final int activeConnections;
        private final long totalPacketsReceived;
        private final long totalPacketsSent;
        private final long crcErrors;
        private final long decodeErrors;
        private final long connectionErrors;
        private final double averageProcessingTime;
        private final long maxProcessingTime;
        private final double currentQPS;
        private final double errorRate;
        private final long uptime;
        private final int onlineDevices;
        private final ConcurrentHashMap<Byte, AtomicLong> packetTypeStats;
        
        public MetricsSnapshot(long totalConnections, int activeConnections, 
                             long totalPacketsReceived, long totalPacketsSent,
                             long crcErrors, long decodeErrors, long connectionErrors,
                             double averageProcessingTime, long maxProcessingTime,
                             double currentQPS, double errorRate, long uptime,
                             int onlineDevices, ConcurrentHashMap<Byte, AtomicLong> packetTypeStats) {
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.totalPacketsReceived = totalPacketsReceived;
            this.totalPacketsSent = totalPacketsSent;
            this.crcErrors = crcErrors;
            this.decodeErrors = decodeErrors;
            this.connectionErrors = connectionErrors;
            this.averageProcessingTime = averageProcessingTime;
            this.maxProcessingTime = maxProcessingTime;
            this.currentQPS = currentQPS;
            this.errorRate = errorRate;
            this.uptime = uptime;
            this.onlineDevices = onlineDevices;
            this.packetTypeStats = packetTypeStats;
        }
        
        // Getter方法
        public long getTotalConnections() { return totalConnections; }
        public int getActiveConnections() { return activeConnections; }
        public long getTotalPacketsReceived() { return totalPacketsReceived; }
        public long getTotalPacketsSent() { return totalPacketsSent; }
        public long getCrcErrors() { return crcErrors; }
        public long getDecodeErrors() { return decodeErrors; }
        public long getConnectionErrors() { return connectionErrors; }
        public double getAverageProcessingTime() { return averageProcessingTime; }
        public long getMaxProcessingTime() { return maxProcessingTime; }
        public double getCurrentQPS() { return currentQPS; }
        public double getErrorRate() { return errorRate; }
        public long getUptime() { return uptime; }
        public int getOnlineDevices() { return onlineDevices; }
        public ConcurrentHashMap<Byte, AtomicLong> getPacketTypeStats() { return packetTypeStats; }
    }
}