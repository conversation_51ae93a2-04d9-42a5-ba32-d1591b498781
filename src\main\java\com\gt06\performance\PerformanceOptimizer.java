package com.gt06.performance;

import com.gt06.monitor.ServerMetrics;
import com.gt06.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.ThreadMXBean;
import java.lang.management.GarbageCollectorMXBean;
import java.util.List;

/**
 * 性能优化器
 * 自动监控和优化系统性能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PerformanceOptimizer {
    
    private static final Logger logger = LoggerFactory.getLogger(PerformanceOptimizer.class);
    
    /** 单例实例 */
    private static volatile PerformanceOptimizer instance;
    
    /** 线程池管理器 */
    private final ThreadPoolManager threadPoolManager;
    
    /** 内存管理器 */
    private final MemoryManager memoryManager;
    
    /** 连接管理器 */
    private final ConnectionManager connectionManager;
    
    /** 缓存管理器 */
    private final CacheManager cacheManager;
    
    /** 性能监控器 */
    private final PerformanceMonitor performanceMonitor;
    
    /** 定时优化执行器 */
    private final ScheduledExecutorService optimizerScheduler = Executors.newScheduledThreadPool(2);
    
    /** 运行状态 */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /** JVM管理Bean */
    private final MemoryMXBean memoryMXBean;
    private final ThreadMXBean threadMXBean;
    private final List<GarbageCollectorMXBean> gcMXBeans;
    
    private PerformanceOptimizer() {
        this.threadPoolManager = new ThreadPoolManager();
        this.memoryManager = new MemoryManager();
        this.connectionManager = new ConnectionManager();
        this.cacheManager = new CacheManager();
        this.performanceMonitor = new PerformanceMonitor();
        
        // 初始化JVM管理Bean
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
        this.threadMXBean = ManagementFactory.getThreadMXBean();
        this.gcMXBeans = ManagementFactory.getGarbageCollectorMXBeans();
    }
    
    /**
     * 获取单例实例
     */
    public static PerformanceOptimizer getInstance() {
        if (instance == null) {
            synchronized (PerformanceOptimizer.class) {
                if (instance == null) {
                    instance = new PerformanceOptimizer();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动性能优化器
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            performanceMonitor.start();
            
            // 每分钟执行一次性能优化
            optimizerScheduler.scheduleAtFixedRate(this::performOptimization, 60, 60, TimeUnit.SECONDS);
            
            // 每5分钟执行一次深度优化
            optimizerScheduler.scheduleAtFixedRate(this::performDeepOptimization, 300, 300, TimeUnit.SECONDS);
            
            logger.info("性能优化器已启动");
        }
    }
    
    /**
     * 停止性能优化器
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            performanceMonitor.stop();
            
            optimizerScheduler.shutdown();
            try {
                if (!optimizerScheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    optimizerScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                optimizerScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            logger.info("性能优化器已停止");
        }
    }
    
    /**
     * 执行性能优化
     */
    private void performOptimization() {
        try {
            logger.debug("开始执行性能优化");
            
            // 线程池优化
            threadPoolManager.optimize();
            
            // 内存优化
            memoryManager.optimize();
            
            // 连接优化
            connectionManager.optimize();
            
            // 缓存优化
            cacheManager.optimize();
            
            logger.debug("性能优化完成");
            
        } catch (Exception e) {
            logger.error("执行性能优化时发生异常", e);
        }
    }
    
    /**
     * 执行深度优化
     */
    private void performDeepOptimization() {
        try {
            logger.info("开始执行深度性能优化");
            
            // 强制垃圾回收
            if (shouldForceGC()) {
                logger.info("执行强制垃圾回收");
                System.gc();
            }
            
            // 线程池深度优化
            threadPoolManager.deepOptimize();
            
            // 内存深度优化
            memoryManager.deepOptimize();
            
            // 缓存深度清理
            cacheManager.deepCleanup();
            
            logger.info("深度性能优化完成");
            
        } catch (Exception e) {
            logger.error("执行深度性能优化时发生异常", e);
        }
    }
    
    /**
     * 判断是否应该强制垃圾回收
     */
    private boolean shouldForceGC() {
        long usedMemory = memoryMXBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryMXBean.getHeapMemoryUsage().getMax();
        double memoryUsage = (double) usedMemory / maxMemory;
        
        return memoryUsage > 0.8; // 内存使用率超过80%时强制GC
    }
    
    /**
     * 获取线程池管理器
     */
    public ThreadPoolManager getThreadPoolManager() {
        return threadPoolManager;
    }
    
    /**
     * 获取内存管理器
     */
    public MemoryManager getMemoryManager() {
        return memoryManager;
    }
    
    /**
     * 获取连接管理器
     */
    public ConnectionManager getConnectionManager() {
        return connectionManager;
    }
    
    /**
     * 获取缓存管理器
     */
    public CacheManager getCacheManager() {
        return cacheManager;
    }
    
    /**
     * 线程池管理器
     */
    public static class ThreadPoolManager {
        private final Map<String, ThreadPoolExecutor> threadPools = new ConcurrentHashMap<>();
        private final Map<String, ThreadPoolMetrics> poolMetrics = new ConcurrentHashMap<>();
        
        /**
         * 注册线程池
         */
        public void registerThreadPool(String name, ThreadPoolExecutor executor) {
            threadPools.put(name, executor);
            poolMetrics.put(name, new ThreadPoolMetrics(name));
            logger.info("注册线程池: {}", name);
        }
        
        /**
         * 优化线程池
         */
        public void optimize() {
            for (Map.Entry<String, ThreadPoolExecutor> entry : threadPools.entrySet()) {
                String name = entry.getKey();
                ThreadPoolExecutor executor = entry.getValue();
                ThreadPoolMetrics metrics = poolMetrics.get(name);
                
                if (metrics != null) {
                    metrics.update(executor);
                    optimizeThreadPool(name, executor, metrics);
                }
            }
        }
        
        /**
         * 深度优化线程池
         */
        public void deepOptimize() {
            for (Map.Entry<String, ThreadPoolExecutor> entry : threadPools.entrySet()) {
                String name = entry.getKey();
                ThreadPoolExecutor executor = entry.getValue();
                
                // 清理已完成的任务
                executor.purge();
                
                // 动态调整核心线程数
                adjustCorePoolSize(name, executor);
            }
        }
        
        /**
         * 优化单个线程池
         */
        private void optimizeThreadPool(String name, ThreadPoolExecutor executor, ThreadPoolMetrics metrics) {
            int activeCount = executor.getActiveCount();
            int corePoolSize = executor.getCorePoolSize();
            int maximumPoolSize = executor.getMaximumPoolSize();
            int queueSize = executor.getQueue().size();
            
            // 如果活跃线程数接近最大值且队列中有任务，考虑增加线程
            if (activeCount >= corePoolSize * 0.8 && queueSize > 0) {
                if (corePoolSize < maximumPoolSize) {
                    int newCoreSize = Math.min(corePoolSize + 1, maximumPoolSize);
                    executor.setCorePoolSize(newCoreSize);
                    logger.debug("线程池 {} 核心线程数调整为: {}", name, newCoreSize);
                }
            }
            
            // 如果活跃线程数很低，考虑减少线程
            else if (activeCount < corePoolSize * 0.3 && corePoolSize > 1) {
                int newCoreSize = Math.max(corePoolSize - 1, 1);
                executor.setCorePoolSize(newCoreSize);
                logger.debug("线程池 {} 核心线程数调整为: {}", name, newCoreSize);
            }
        }
        
        /**
         * 调整核心线程池大小
         */
        private void adjustCorePoolSize(String name, ThreadPoolExecutor executor) {
            ConfigManager configManager = ConfigManager.getInstance();
            int configuredSize = configManager.getInt("server.thread.pool.size", 50);
            
            if (executor.getCorePoolSize() != configuredSize) {
                executor.setCorePoolSize(configuredSize);
                logger.info("线程池 {} 核心线程数调整为配置值: {}", name, configuredSize);
            }
        }
        
        /**
         * 获取线程池状态
         */
        public Map<String, ThreadPoolStatus> getThreadPoolStatus() {
            Map<String, ThreadPoolStatus> statusMap = new ConcurrentHashMap<>();
            
            for (Map.Entry<String, ThreadPoolExecutor> entry : threadPools.entrySet()) {
                String name = entry.getKey();
                ThreadPoolExecutor executor = entry.getValue();
                
                statusMap.put(name, new ThreadPoolStatus(
                    name,
                    executor.getCorePoolSize(),
                    executor.getMaximumPoolSize(),
                    executor.getActiveCount(),
                    executor.getQueue().size(),
                    executor.getCompletedTaskCount()
                ));
            }
            
            return statusMap;
        }
        
        /**
         * 线程池指标类
         */
        private static class ThreadPoolMetrics {
            private final String name;
            private final AtomicLong totalTasks = new AtomicLong(0);
            private final AtomicLong completedTasks = new AtomicLong(0);
            private volatile long lastUpdateTime = System.currentTimeMillis();
            
            public ThreadPoolMetrics(String name) {
                this.name = name;
            }
            
            public void update(ThreadPoolExecutor executor) {
                totalTasks.set(executor.getTaskCount());
                completedTasks.set(executor.getCompletedTaskCount());
                lastUpdateTime = System.currentTimeMillis();
            }
            
            public String getName() { return name; }
            public long getTotalTasks() { return totalTasks.get(); }
            public long getCompletedTasks() { return completedTasks.get(); }
            public long getLastUpdateTime() { return lastUpdateTime; }
        }
        
        /**
         * 线程池状态类
         */
        public static class ThreadPoolStatus {
            private final String name;
            private final int corePoolSize;
            private final int maximumPoolSize;
            private final int activeCount;
            private final int queueSize;
            private final long completedTaskCount;
            
            public ThreadPoolStatus(String name, int corePoolSize, int maximumPoolSize,
                                  int activeCount, int queueSize, long completedTaskCount) {
                this.name = name;
                this.corePoolSize = corePoolSize;
                this.maximumPoolSize = maximumPoolSize;
                this.activeCount = activeCount;
                this.queueSize = queueSize;
                this.completedTaskCount = completedTaskCount;
            }
            
            public String getName() { return name; }
            public int getCorePoolSize() { return corePoolSize; }
            public int getMaximumPoolSize() { return maximumPoolSize; }
            public int getActiveCount() { return activeCount; }
            public int getQueueSize() { return queueSize; }
            public long getCompletedTaskCount() { return completedTaskCount; }
        }
    }
    
    /**
     * 内存管理器
     */
    public static class MemoryManager {
        private final AtomicLong lastGCTime = new AtomicLong(0);
        private final AtomicInteger gcCount = new AtomicInteger(0);
        
        /**
         * 内存优化
         */
        public void optimize() {
            // 检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            double memoryUsage = (double) usedMemory / maxMemory;
            
            if (memoryUsage > 0.75) {
                logger.warn("内存使用率较高: {:.2f}%", memoryUsage * 100);
                
                // 建议垃圾回收
                if (System.currentTimeMillis() - lastGCTime.get() > 60000) { // 1分钟内只建议一次GC
                    System.gc();
                    lastGCTime.set(System.currentTimeMillis());
                    gcCount.incrementAndGet();
                    logger.info("建议执行垃圾回收，当前内存使用率: {:.2f}%", memoryUsage * 100);
                }
            }
        }
        
        /**
         * 深度内存优化
         */
        public void deepOptimize() {
            // 强制垃圾回收
            System.gc();
            
            // 等待GC完成
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 再次检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            double memoryUsage = (double) usedMemory / maxMemory;
            
            logger.info("深度内存优化完成，当前内存使用率: {:.2f}%", memoryUsage * 100);
        }
        
        /**
         * 获取内存状态
         */
        public MemoryStatus getMemoryStatus() {
            Runtime runtime = Runtime.getRuntime();
            return new MemoryStatus(
                runtime.maxMemory(),
                runtime.totalMemory(),
                runtime.freeMemory(),
                gcCount.get()
            );
        }
        
        /**
         * 内存状态类
         */
        public static class MemoryStatus {
            private final long maxMemory;
            private final long totalMemory;
            private final long freeMemory;
            private final int gcCount;
            
            public MemoryStatus(long maxMemory, long totalMemory, long freeMemory, int gcCount) {
                this.maxMemory = maxMemory;
                this.totalMemory = totalMemory;
                this.freeMemory = freeMemory;
                this.gcCount = gcCount;
            }
            
            public long getMaxMemory() { return maxMemory; }
            public long getTotalMemory() { return totalMemory; }
            public long getFreeMemory() { return freeMemory; }
            public long getUsedMemory() { return totalMemory - freeMemory; }
            public double getMemoryUsage() { return (double) getUsedMemory() / maxMemory; }
            public int getGcCount() { return gcCount; }
        }
    }
    
    /**
     * 连接管理器
     */
    public static class ConnectionManager {
        private final Map<String, ConnectionPool> connectionPools = new ConcurrentHashMap<>();
        
        /**
         * 注册连接池
         */
        public void registerConnectionPool(String name, ConnectionPool pool) {
            connectionPools.put(name, pool);
            logger.info("注册连接池: {}", name);
        }
        
        /**
         * 连接优化
         */
        public void optimize() {
            for (Map.Entry<String, ConnectionPool> entry : connectionPools.entrySet()) {
                String name = entry.getKey();
                ConnectionPool pool = entry.getValue();
                
                // 清理空闲连接
                int cleaned = pool.cleanupIdleConnections();
                if (cleaned > 0) {
                    logger.debug("连接池 {} 清理了 {} 个空闲连接", name, cleaned);
                }
            }
        }
        
        /**
         * 连接池接口
         */
        public interface ConnectionPool {
            int cleanupIdleConnections();
            int getActiveConnections();
            int getIdleConnections();
            int getMaxConnections();
        }
    }
    
    /**
     * 缓存管理器
     */
    public static class CacheManager {
        private final Map<String, Cache> caches = new ConcurrentHashMap<>();
        
        /**
         * 注册缓存
         */
        public void registerCache(String name, Cache cache) {
            caches.put(name, cache);
            logger.info("注册缓存: {}", name);
        }
        
        /**
         * 缓存优化
         */
        public void optimize() {
            for (Map.Entry<String, Cache> entry : caches.entrySet()) {
                String name = entry.getKey();
                Cache cache = entry.getValue();
                
                // 清理过期缓存
                int cleaned = cache.cleanupExpired();
                if (cleaned > 0) {
                    logger.debug("缓存 {} 清理了 {} 个过期项", name, cleaned);
                }
            }
        }
        
        /**
         * 深度清理缓存
         */
        public void deepCleanup() {
            for (Map.Entry<String, Cache> entry : caches.entrySet()) {
                String name = entry.getKey();
                Cache cache = entry.getValue();
                
                // 清理LRU缓存
                int cleaned = cache.cleanupLRU();
                logger.info("缓存 {} 深度清理了 {} 个项", name, cleaned);
            }
        }
        
        /**
         * 缓存接口
         */
        public interface Cache {
            int cleanupExpired();
            int cleanupLRU();
            int size();
            double getHitRate();
        }
    }
    
    /**
     * 性能监控器
     */
    public static class PerformanceMonitor {
        private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        private final AtomicBoolean running = new AtomicBoolean(false);
        
        public void start() {
            if (running.compareAndSet(false, true)) {
                scheduler.scheduleAtFixedRate(this::collectMetrics, 30, 30, TimeUnit.SECONDS);
            }
        }
        
        public void stop() {
            if (running.compareAndSet(true, false)) {
                scheduler.shutdown();
            }
        }
        
        private void collectMetrics() {
            // 收集性能指标
            // 这里可以扩展更多的性能监控逻辑
        }
    }
}