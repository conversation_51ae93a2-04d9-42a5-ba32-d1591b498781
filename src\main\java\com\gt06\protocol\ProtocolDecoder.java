package com.gt06.protocol;

import com.gt06.model.*;
import com.gt06.util.CrcUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

/**
 * GT06协议解码器
 * 负责将字节数据解码为协议包对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ProtocolDecoder {
    
    private static final Logger logger = LoggerFactory.getLogger(ProtocolDecoder.class);
    
    /**
     * 解码协议包
     * 
     * @param data 完整的协议包字节数据
     * @return 解码后的协议包对象，解码失败返回null
     */
    public static ProtocolPacket decode(byte[] data) {
        if (data == null || data.length < 10) {
            logger.warn("数据包长度不足，最小长度为10字节");
            return null;
        }
        
        try {
            // 验证起始位
            if (!verifyStartBit(data)) {
                logger.warn("起始位验证失败");
                return null;
            }
            
            // 验证停止位
            if (!verifyStopBit(data)) {
                logger.warn("停止位验证失败");
                return null;
            }
            
            // 提取包长度
            int packetLength = data[2] & 0xFF;
            if (data.length != packetLength + 5) { // 总长度 = 起始位(2) + 包长度(1) + 数据内容(packetLength) + 停止位(2)
                logger.warn("数据包长度不匹配，期望: {}, 实际: {}", packetLength + 5, data.length);
                return null;
            }
            
            // 提取协议号
            byte protocolNumber = data[3];
            
            // 提取信息序列号（倒数第4、5字节）
            int serialNumber = ((data[data.length - 4] & 0xFF) << 8) | (data[data.length - 3] & 0xFF);
            
            // 验证CRC校验
            byte[] packetData = Arrays.copyOfRange(data, 2, data.length - 2); // 从包长度到信息序列号
            if (!CrcUtil.verifyPacketCrc(packetData)) {
                logger.warn("CRC校验失败");
                return null;
            }
            
            // 根据协议号创建对应的协议包对象
            ProtocolPacket packet = createPacketByProtocolNumber(protocolNumber, serialNumber);
            if (packet == null) {
                logger.warn("不支持的协议号: 0x{}", String.format("%02X", protocolNumber));
                return null;
            }
            
            // 设置基础属性
            packet.setLength(packetLength);
            packet.setSerialNumber(serialNumber);
            packet.setCrc(CrcUtil.bytesToCrc(data, data.length - 4));
            
            // 解析信息内容
            int infoContentLength = packetLength - 5; // 减去协议号(1) + 信息序列号(2) + CRC(2)
            if (infoContentLength > 0) {
                packet.parseFromBytes(data, 4); // 从协议号后开始解析
            }
            
            logger.debug("成功解码协议包: {}", packet);
            return packet;
            
        } catch (Exception e) {
            logger.error("解码协议包时发生异常", e);
            return null;
        }
    }
    
    /**
     * 验证起始位
     */
    private static boolean verifyStartBit(byte[] data) {
        return data.length >= 2 && 
               data[0] == ProtocolPacket.START_BIT[0] && 
               data[1] == ProtocolPacket.START_BIT[1];
    }
    
    /**
     * 验证停止位
     */
    private static boolean verifyStopBit(byte[] data) {
        return data.length >= 2 && 
               data[data.length - 2] == ProtocolPacket.STOP_BIT[0] && 
               data[data.length - 1] == ProtocolPacket.STOP_BIT[1];
    }
    
    /**
     * 根据协议号创建对应的协议包对象
     */
    private static ProtocolPacket createPacketByProtocolNumber(byte protocolNumber, int serialNumber) {
        switch (protocolNumber) {
            case LoginPacket.PROTOCOL_NUMBER:
                return new LoginPacket();
                
            case LocationPacket.PROTOCOL_NUMBER:
                return new LocationPacket(serialNumber);
                
            case HeartbeatPacket.PROTOCOL_NUMBER:
                return new HeartbeatPacket(serialNumber);
                
            case AlarmPacket.PROTOCOL_NUMBER:
                return new AlarmPacket(serialNumber);
                
            case CommandResponsePacket.PROTOCOL_NUMBER:
                return new CommandResponsePacket(serialNumber);
                
            default:
                return null;
        }
    }
    
    /**
     * 检查数据包是否完整
     * 
     * @param buffer 缓冲区数据
     * @return 完整数据包的长度，如果不完整返回-1
     */
    public static int checkPacketComplete(byte[] buffer) {
        if (buffer == null || buffer.length < 3) {
            return -1;
        }
        
        // 查找起始位
        int startIndex = -1;
        for (int i = 0; i <= buffer.length - 2; i++) {
            if (buffer[i] == ProtocolPacket.START_BIT[0] && 
                buffer[i + 1] == ProtocolPacket.START_BIT[1]) {
                startIndex = i;
                break;
            }
        }
        
        if (startIndex == -1 || startIndex + 3 >= buffer.length) {
            return -1;
        }
        
        // 获取包长度
        int packetLength = buffer[startIndex + 2] & 0xFF;
        int totalLength = packetLength + 5; // 起始位(2) + 包长度(1) + 数据内容(packetLength) + 停止位(2)
        
        // 检查是否有足够的数据
        if (startIndex + totalLength > buffer.length) {
            return -1;
        }
        
        // 验证停止位
        int stopIndex = startIndex + totalLength - 2;
        if (buffer[stopIndex] == ProtocolPacket.STOP_BIT[0] && 
            buffer[stopIndex + 1] == ProtocolPacket.STOP_BIT[1]) {
            return totalLength;
        }
        
        return -1;
    }
    
    /**
     * 从缓冲区中提取完整的数据包
     * 
     * @param buffer 缓冲区数据
     * @return 提取的数据包，如果没有完整包返回null
     */
    public static byte[] extractPacket(byte[] buffer) {
        int packetLength = checkPacketComplete(buffer);
        if (packetLength > 0) {
            return Arrays.copyOfRange(buffer, 0, packetLength);
        }
        return null;
    }
}