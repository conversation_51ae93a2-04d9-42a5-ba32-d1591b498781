package com.gt06.protocol;

import com.gt06.model.ProtocolPacket;
import com.gt06.util.CrcUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * GT06协议编码器
 * 负责将协议包对象编码为字节数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ProtocolEncoder {
    
    private static final Logger logger = LoggerFactory.getLogger(ProtocolEncoder.class);
    
    /**
     * 编码协议包为字节数组
     * 
     * @param packet 协议包对象
     * @return 编码后的字节数组，编码失败返回null
     */
    public static byte[] encode(ProtocolPacket packet) {
        if (packet == null) {
            logger.warn("协议包对象为空");
            return null;
        }
        
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // 1. 写入起始位
            baos.write(ProtocolPacket.START_BIT);
            
            // 2. 获取信息内容
            byte[] infoContent = packet.getInfoContent();
            if (infoContent == null) {
                infoContent = new byte[0];
            }
            
            // 3. 计算包长度：协议号(1) + 信息内容长度 + 信息序列号(2) + CRC(2)
            int packetLength = 1 + infoContent.length + 2 + 2;
            baos.write(packetLength);
            
            // 4. 写入协议号
            baos.write(packet.getProtocolNumber());
            
            // 5. 写入信息内容
            if (infoContent.length > 0) {
                baos.write(infoContent);
            }
            
            // 6. 写入信息序列号（2字节，大端序）
            int serialNumber = packet.getSerialNumber();
            baos.write((serialNumber >> 8) & 0xFF);
            baos.write(serialNumber & 0xFF);
            
            // 7. 计算并写入CRC校验值
            byte[] dataForCrc = baos.toByteArray();
            // CRC计算范围：从包长度到信息序列号（不包括起始位）
            byte[] crcData = new byte[dataForCrc.length - 2]; // 减去起始位的2字节
            System.arraycopy(dataForCrc, 2, crcData, 0, crcData.length);
            
            int crc = CrcUtil.calculateCrc16(crcData);
            byte[] crcBytes = CrcUtil.crcToBytes(crc);
            baos.write(crcBytes);
            
            // 8. 写入停止位
            baos.write(ProtocolPacket.STOP_BIT);
            
            byte[] result = baos.toByteArray();
            logger.debug("成功编码协议包，长度: {} 字节", result.length);
            
            return result;
            
        } catch (IOException e) {
            logger.error("编码协议包时发生异常", e);
            return null;
        }
    }
    
    /**
     * 创建服务器响应包
     * 
     * @param originalPacket 原始请求包
     * @param responseProtocolNumber 响应协议号
     * @param responseContent 响应内容
     * @return 编码后的响应包字节数组
     */
    public static byte[] createResponse(ProtocolPacket originalPacket, byte responseProtocolNumber, byte[] responseContent) {
        if (originalPacket == null) {
            logger.warn("原始协议包为空");
            return null;
        }
        
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // 1. 写入起始位
            baos.write(ProtocolPacket.START_BIT);
            
            // 2. 计算包长度
            int contentLength = responseContent != null ? responseContent.length : 0;
            int packetLength = 1 + contentLength + 2 + 2; // 协议号 + 内容 + 序列号 + CRC
            baos.write(packetLength);
            
            // 3. 写入响应协议号
            baos.write(responseProtocolNumber);
            
            // 4. 写入响应内容
            if (responseContent != null && responseContent.length > 0) {
                baos.write(responseContent);
            }
            
            // 5. 写入信息序列号（使用原始包的序列号）
            int serialNumber = originalPacket.getSerialNumber();
            baos.write((serialNumber >> 8) & 0xFF);
            baos.write(serialNumber & 0xFF);
            
            // 6. 计算并写入CRC校验值
            byte[] dataForCrc = baos.toByteArray();
            byte[] crcData = new byte[dataForCrc.length - 2]; // 减去起始位
            System.arraycopy(dataForCrc, 2, crcData, 0, crcData.length);
            
            int crc = CrcUtil.calculateCrc16(crcData);
            byte[] crcBytes = CrcUtil.crcToBytes(crc);
            baos.write(crcBytes);
            
            // 7. 写入停止位
            baos.write(ProtocolPacket.STOP_BIT);
            
            byte[] result = baos.toByteArray();
            logger.debug("成功创建响应包，协议号: 0x{}, 长度: {} 字节", 
                        String.format("%02X", responseProtocolNumber), result.length);
            
            return result;
            
        } catch (IOException e) {
            logger.error("创建响应包时发生异常", e);
            return null;
        }
    }
    
    /**
     * 创建登录响应包
     * 
     * @param loginPacket 登录请求包
     * @return 登录响应包字节数组
     */
    public static byte[] createLoginResponse(ProtocolPacket loginPacket) {
        return createResponse(loginPacket, (byte) 0x01, null);
    }
    
    /**
     * 创建心跳响应包
     * 
     * @param heartbeatPacket 心跳请求包
     * @return 心跳响应包字节数组
     */
    public static byte[] createHeartbeatResponse(ProtocolPacket heartbeatPacket) {
        return createResponse(heartbeatPacket, (byte) 0x13, null);
    }
    
    /**
     * 创建定位数据响应包
     * 
     * @param locationPacket 定位数据包
     * @return 定位数据响应包字节数组
     */
    public static byte[] createLocationResponse(ProtocolPacket locationPacket) {
        return createResponse(locationPacket, (byte) 0x12, null);
    }
    
    /**
     * 创建报警数据响应包
     * 
     * @param alarmPacket 报警数据包
     * @return 报警数据响应包字节数组
     */
    public static byte[] createAlarmResponse(ProtocolPacket alarmPacket) {
        return createResponse(alarmPacket, (byte) 0x16, null);
    }
    
    /**
     * 创建指令下发包
     * 
     * @param serialNumber 信息序列号
     * @param command 指令内容
     * @return 指令包字节数组
     */
    public static byte[] createCommandPacket(int serialNumber, String command) {
        if (command == null || command.isEmpty()) {
            logger.warn("指令内容为空");
            return null;
        }
        
        try {
            byte[] commandBytes = command.getBytes("UTF-8");
            
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // 1. 写入起始位
            baos.write(ProtocolPacket.START_BIT);
            
            // 2. 计算包长度
            int packetLength = 1 + commandBytes.length + 2 + 2; // 协议号 + 指令 + 序列号 + CRC
            baos.write(packetLength);
            
            // 3. 写入协议号（指令下发：0x80）
            baos.write(0x80);
            
            // 4. 写入指令内容
            baos.write(commandBytes);
            
            // 5. 写入信息序列号
            baos.write((serialNumber >> 8) & 0xFF);
            baos.write(serialNumber & 0xFF);
            
            // 6. 计算并写入CRC校验值
            byte[] dataForCrc = baos.toByteArray();
            byte[] crcData = new byte[dataForCrc.length - 2]; // 减去起始位
            System.arraycopy(dataForCrc, 2, crcData, 0, crcData.length);
            
            int crc = CrcUtil.calculateCrc16(crcData);
            byte[] crcBytes = CrcUtil.crcToBytes(crc);
            baos.write(crcBytes);
            
            // 7. 写入停止位
            baos.write(ProtocolPacket.STOP_BIT);
            
            byte[] result = baos.toByteArray();
            logger.debug("成功创建指令包，指令: {}, 长度: {} 字节", command, result.length);
            
            return result;
            
        } catch (Exception e) {
            logger.error("创建指令包时发生异常", e);
            return null;
        }
    }
    
    /**
     * 将字节数组转换为十六进制字符串（用于调试）
     * 
     * @param data 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHexString(byte[] data) {
        if (data == null) {
            return "null";
        }
        
        StringBuilder sb = new StringBuilder();
        for (byte b : data) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }
}