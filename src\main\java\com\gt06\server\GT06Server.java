package com.gt06.server;

import com.gt06.handler.PacketHandler;
import com.gt06.protocol.ProtocolDecoder;
import com.gt06.protocol.ProtocolEncoder;
import com.gt06.model.ProtocolPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * GT06协议TCP服务器
 * 负责接收和处理设备连接及数据通信
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class GT06Server {
    
    private static final Logger logger = LoggerFactory.getLogger(GT06Server.class);
    
    /** 默认服务端口 */
    private static final int DEFAULT_PORT = 8841;
    
    /** 服务器套接字 */
    private ServerSocket serverSocket;
    
    /** 服务端口 */
    private final int port;
    
    /** 服务器运行状态 */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /** 连接计数器 */
    private final AtomicInteger connectionCount = new AtomicInteger(0);
    
    /** 线程池 */
    private ExecutorService executorService;
    
    /** 数据包处理器 */
    private final PacketHandler packetHandler;
    
    /**
     * 构造函数
     */
    public GT06Server() {
        this(DEFAULT_PORT);
    }
    
    /**
     * 构造函数
     * 
     * @param port 服务端口
     */
    public GT06Server(int port) {
        this.port = port;
        this.packetHandler = new PacketHandler();
        
        // 创建线程池
        this.executorService = new ThreadPoolExecutor(
            10,                    // 核心线程数
            100,                   // 最大线程数
            60L,                   // 空闲线程存活时间
            TimeUnit.SECONDS,      // 时间单位
            new LinkedBlockingQueue<>(1000), // 工作队列
            new ThreadFactory() {  // 线程工厂
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "GT06-Worker-" + threadNumber.getAndIncrement());
                    t.setDaemon(false);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }
    
    /**
     * 启动服务器
     */
    public void start() {
        if (running.get()) {
            logger.warn("服务器已经在运行中");
            return;
        }
        
        try {
            serverSocket = new ServerSocket(port);
            running.set(true);
            
            logger.info("GT06协议服务器启动成功，监听端口: {}", port);
            
            // 启动接受连接的线程
            Thread acceptThread = new Thread(this::acceptConnections, "GT06-Accept-Thread");
            acceptThread.start();
            
        } catch (IOException e) {
            logger.error("启动服务器失败", e);
            running.set(false);
        }
    }
    
    /**
     * 停止服务器
     */
    public void stop() {
        if (!running.get()) {
            logger.warn("服务器未在运行");
            return;
        }
        
        running.set(false);
        
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
            
            // 关闭线程池
            executorService.shutdown();
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
            
            logger.info("GT06协议服务器已停止");
            
        } catch (Exception e) {
            logger.error("停止服务器时发生异常", e);
        }
    }
    
    /**
     * 接受客户端连接
     */
    private void acceptConnections() {
        while (running.get()) {
            try {
                Socket clientSocket = serverSocket.accept();
                int connId = connectionCount.incrementAndGet();
                
                logger.info("新设备连接 [{}]: {}", connId, clientSocket.getRemoteSocketAddress());
                
                // 提交到线程池处理
                executorService.submit(new ClientHandler(clientSocket, connId));
                
            } catch (IOException e) {
                if (running.get()) {
                    logger.error("接受连接时发生异常", e);
                }
            }
        }
    }
    
    /**
     * 客户端连接处理器
     */
    private class ClientHandler implements Runnable {
        
        private final Socket socket;
        private final int connectionId;
        private final Logger clientLogger;
        
        public ClientHandler(Socket socket, int connectionId) {
            this.socket = socket;
            this.connectionId = connectionId;
            this.clientLogger = LoggerFactory.getLogger("GT06-Client-" + connectionId);
        }
        
        @Override
        public void run() {
            try {
                // 设置Socket参数
                socket.setSoTimeout(300000); // 5分钟超时
                socket.setKeepAlive(true);
                
                InputStream inputStream = socket.getInputStream();
                OutputStream outputStream = socket.getOutputStream();
                
                byte[] buffer = new byte[1024];
                ByteArrayOutputStream dataBuffer = new ByteArrayOutputStream();
                
                clientLogger.info("开始处理设备连接");
                
                while (running.get() && !socket.isClosed()) {
                    try {
                        int bytesRead = inputStream.read(buffer);
                        if (bytesRead == -1) {
                            clientLogger.info("设备断开连接");
                            break;
                        }
                        
                        // 将数据添加到缓冲区
                        dataBuffer.write(buffer, 0, bytesRead);
                        
                        // 尝试解析完整的数据包
                        processDataBuffer(dataBuffer, outputStream);
                        
                    } catch (SocketTimeoutException e) {
                        clientLogger.debug("读取数据超时");
                    } catch (IOException e) {
                        clientLogger.warn("读取数据时发生异常: {}", e.getMessage());
                        break;
                    }
                }
                
            } catch (Exception e) {
                clientLogger.error("处理客户端连接时发生异常", e);
            } finally {
                closeConnection();
            }
        }
        
        /**
         * 处理数据缓冲区中的数据包
         */
        private void processDataBuffer(ByteArrayOutputStream dataBuffer, OutputStream outputStream) {
            byte[] bufferData = dataBuffer.toByteArray();
            
            while (bufferData.length > 0) {
                // 检查是否有完整的数据包
                int packetLength = ProtocolDecoder.checkPacketComplete(bufferData);
                if (packetLength <= 0) {
                    break; // 没有完整的数据包，等待更多数据
                }
                
                // 提取完整的数据包
                byte[] packetData = new byte[packetLength];
                System.arraycopy(bufferData, 0, packetData, 0, packetLength);
                
                // 处理数据包
                processPacket(packetData, outputStream);
                
                // 从缓冲区中移除已处理的数据
                byte[] remainingData = new byte[bufferData.length - packetLength];
                System.arraycopy(bufferData, packetLength, remainingData, 0, remainingData.length);
                
                // 重置缓冲区
                dataBuffer.reset();
                if (remainingData.length > 0) {
                    dataBuffer.write(remainingData, 0, remainingData.length);
                }
                
                bufferData = dataBuffer.toByteArray();
            }
        }
        
        /**
         * 处理单个数据包
         */
        private void processPacket(byte[] packetData, OutputStream outputStream) {
            try {
                clientLogger.debug("收到数据包: {}", ProtocolEncoder.bytesToHexString(packetData));
                
                // 解码数据包
                ProtocolPacket packet = ProtocolDecoder.decode(packetData);
                if (packet == null) {
                    clientLogger.warn("数据包解码失败");
                    return;
                }
                
                clientLogger.info("解码成功: {}", packet);
                
                // 处理数据包并获取响应
                byte[] response = packetHandler.handlePacket(packet, connectionId);
                if (response != null) {
                    outputStream.write(response);
                    outputStream.flush();
                    
                    clientLogger.debug("发送响应: {}", ProtocolEncoder.bytesToHexString(response));
                }
                
            } catch (Exception e) {
                clientLogger.error("处理数据包时发生异常", e);
            }
        }
        
        /**
         * 关闭连接
         */
        private void closeConnection() {
            try {
                if (socket != null && !socket.isClosed()) {
                    socket.close();
                }
                clientLogger.info("设备连接已关闭");
            } catch (IOException e) {
                clientLogger.error("关闭连接时发生异常", e);
            }
        }
    }
    
    /**
     * 获取服务器运行状态
     */
    public boolean isRunning() {
        return running.get();
    }
    
    /**
     * 获取当前连接数
     */
    public int getConnectionCount() {
        return connectionCount.get();
    }
    
    /**
     * 获取服务端口
     */
    public int getPort() {
        return port;
    }
}