# GT06协议服务器 JDK21 配置文件
# 使用JDK21特性优化的配置

# 服务器配置
server.port=8841
server.management.port=8842
server.name=GT06-Protocol-Server-JDK21

# 线程池配置 - 使用虚拟线程
server.thread.virtual.enabled=true
server.thread.pool.core.size=10
server.thread.pool.max.size=200
server.thread.pool.queue.capacity=1000
server.thread.pool.keep.alive.seconds=60

# 性能优化配置
performance.gc.type=ZGC
performance.memory.initial=512m
performance.memory.max=2g
performance.cpu.cores=auto

# 监控配置
monitor.enabled=true
monitor.interval.seconds=30
monitor.metrics.retention.hours=24
monitor.export.prometheus.enabled=true
monitor.export.prometheus.port=9090

# 告警配置
alert.enabled=true
alert.email.enabled=false
alert.webhook.enabled=true
alert.webhook.url=http://localhost:8080/webhook/alerts
alert.rules.error.rate.threshold=0.05
alert.rules.connection.count.threshold=800
alert.rules.response.time.threshold=1000

# 容错配置
fault.tolerance.enabled=true
fault.tolerance.circuit.breaker.enabled=true
fault.tolerance.circuit.breaker.failure.threshold=10
fault.tolerance.circuit.breaker.timeout.seconds=60
fault.tolerance.retry.max.attempts=3
fault.tolerance.retry.delay.seconds=1

# 协议配置
protocol.gt06.version=1.02
protocol.gt06.crc.enabled=true
protocol.gt06.heartbeat.interval.seconds=30
protocol.gt06.login.timeout.seconds=60
protocol.gt06.data.compression.enabled=false

# 数据库配置（可选）
database.enabled=false
database.url=********************************
database.username=gt06_user
database.password=gt06_password
database.pool.min.size=5
database.pool.max.size=20

# 缓存配置
cache.enabled=true
cache.type=caffeine
cache.device.max.size=10000
cache.device.expire.minutes=30
cache.location.max.size=50000
cache.location.expire.minutes=60

# 日志配置
logging.level.root=INFO
logging.level.com.gt06=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n
logging.file.name=logs/gt06-server.log
logging.file.max-size=100MB
logging.file.max-history=30

# 安全配置
security.enabled=false
security.ssl.enabled=false
security.ssl.keystore.path=keystore.jks
security.ssl.keystore.password=changeit
security.authentication.required=false
security.rate.limit.enabled=true
security.rate.limit.requests.per.minute=1000

# JDK21特性配置
jdk21.features.enabled=true
jdk21.virtual.threads.enabled=true
jdk21.pattern.matching.enabled=true
jdk21.record.classes.enabled=true
jdk21.text.blocks.enabled=true
jdk21.switch.expressions.enabled=true

# 开发模式配置
dev.mode.enabled=false
dev.hot.reload.enabled=false
dev.debug.enabled=false
dev.mock.data.enabled=false

# 生产环境配置
prod.optimization.enabled=true
prod.compression.enabled=true
prod.connection.pooling.enabled=true
prod.batch.processing.enabled=true
prod.async.processing.enabled=true