package com.gt06.jdk21;

import com.gt06.model.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JDK21特性的现代化协议处理器测试
 * 使用JUnit5和JDK21新特性
 * 
 * <AUTHOR>
 * @version 2.1.0
 */
@DisplayName("现代化协议处理器测试")
class ModernProtocolHandlerTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ModernProtocolHandlerTest.class);
    
    private ModernProtocolHandler handler;
    
    @BeforeEach
    void setUp() {
        handler = new ModernProtocolHandler();
        logger.info("测试环境初始化完成");
    }
    
    @Nested
    @DisplayName("协议包处理测试")
    class ProtocolPacketProcessingTests {
        
        @Test
        @DisplayName("登录包处理测试")
        void testLoginPacketProcessing() {
            // 创建登录包
            LoginPacket loginPacket = new LoginPacket();
            loginPacket.setDeviceId("GT06-TEST-001");
            loginPacket.setProtocolNumber((byte) 0x01);
            
            // 处理登录包
            ModernProtocolHandler.ProcessResult result = handler.processPacket(loginPacket);
            
            // 验证结果
            assertInstanceOf(ModernProtocolHandler.ProcessResult.Success.class, result);
            
            if (result instanceof ModernProtocolHandler.ProcessResult.Success success) {
                assertTrue(success.message().contains("GT06-TEST-001"));
                assertNotNull(success.timestamp());
                logger.info("登录包处理成功: {}", success.message());
            }
        }
        
        @Test
        @DisplayName("定位包处理测试")
        void testLocationPacketProcessing() {
            // 创建定位包
            LocationPacket locationPacket = new LocationPacket();
            locationPacket.setDeviceId("GT06-TEST-002");
            locationPacket.setProtocolNumber((byte) 0x12);
            locationPacket.setLatitude(39.9042);
            locationPacket.setLongitude(116.4074);
            
            // 处理定位包
            ModernProtocolHandler.ProcessResult result = handler.processPacket(locationPacket);
            
            // 验证结果
            assertInstanceOf(ModernProtocolHandler.ProcessResult.Success.class, result);
            logger.info("定位包处理测试通过");
        }
        
        @Test
        @DisplayName("无效定位包处理测试")
        void testInvalidLocationPacketProcessing() {
            // 创建无效定位包（坐标为0,0）
            LocationPacket invalidLocationPacket = new LocationPacket();
            invalidLocationPacket.setDeviceId("GT06-TEST-003");
            invalidLocationPacket.setProtocolNumber((byte) 0x12);
            invalidLocationPacket.setLatitude(0.0);
            invalidLocationPacket.setLongitude(0.0);
            
            // 处理无效定位包
            ModernProtocolHandler.ProcessResult result = handler.processPacket(invalidLocationPacket);
            
            // 验证结果应该是警告
            assertInstanceOf(ModernProtocolHandler.ProcessResult.Warning.class, result);
            
            if (result instanceof ModernProtocolHandler.ProcessResult.Warning warning) {
                assertTrue(warning.warning().contains("位置数据无效"));
                assertTrue(warning.suggestion().contains("检查GPS信号"));
                logger.info("无效定位包处理警告: {}", warning.warning());
            }
        }
        
        @Test
        @DisplayName("心跳包处理测试")
        void testHeartbeatPacketProcessing() {
            // 创建心跳包
            HeartbeatPacket heartbeatPacket = new HeartbeatPacket();
            heartbeatPacket.setDeviceId("GT06-TEST-004");
            heartbeatPacket.setProtocolNumber((byte) 0x13);
            
            // 处理心跳包
            ModernProtocolHandler.ProcessResult result = handler.processPacket(heartbeatPacket);
            
            // 验证结果
            assertInstanceOf(ModernProtocolHandler.ProcessResult.Success.class, result);
            logger.info("心跳包处理测试通过");
        }
        
        @Test
        @DisplayName("报警包处理测试")
        void testAlarmPacketProcessing() {
            // 创建报警包
            AlarmPacket alarmPacket = new AlarmPacket();
            alarmPacket.setDeviceId("GT06-TEST-005");
            alarmPacket.setProtocolNumber((byte) 0x16);
            alarmPacket.setAlarmType(1); // SOS紧急报警
            
            // 处理报警包
            ModernProtocolHandler.ProcessResult result = handler.processPacket(alarmPacket);
            
            // 验证结果
            assertInstanceOf(ModernProtocolHandler.ProcessResult.Success.class, result);
            
            if (result instanceof ModernProtocolHandler.ProcessResult.Success success) {
                assertTrue(success.message().contains("SOS紧急报警"));
                logger.info("报警包处理成功: {}", success.message());
            }
        }
        
        @Test
        @DisplayName("空协议包处理测试")
        void testNullPacketProcessing() {
            // 处理空协议包
            ModernProtocolHandler.ProcessResult result = handler.processPacket(null);
            
            // 验证结果应该是失败
            assertInstanceOf(ModernProtocolHandler.ProcessResult.Failure.class, result);
            
            if (result instanceof ModernProtocolHandler.ProcessResult.Failure failure) {
                assertTrue(failure.error().contains("协议包为空"));
                assertNotNull(failure.cause());
                logger.info("空协议包处理失败: {}", failure.error());
            }
        }
    }
    
    @Nested
    @DisplayName("设备状态测试")
    class DeviceStatusTests {
        
        @Test
        @DisplayName("有效设备状态创建测试")
        void testValidDeviceStatusCreation() {
            // 创建有效的设备状态
            ModernProtocolHandler.DeviceStatus device = new ModernProtocolHandler.DeviceStatus(
                "GT06-DEVICE-001",
                true,
                Instant.now(),
                39.9042,
                116.4074,
                85,
                ModernProtocolHandler.DeviceType.GPS_TRACKER
            );
            
            // 验证设备状态
            assertEquals("GT06-DEVICE-001", device.deviceId());
            assertTrue(device.online());
            assertTrue(device.isLocationValid());
            assertEquals(ModernProtocolHandler.DeviceType.GPS_TRACKER, device.type());
            
            logger.info("设备状态创建成功: {}", device.deviceId());
        }
        
        @Test
        @DisplayName("无效设备ID测试")
        void testInvalidDeviceId() {
            // 测试空设备ID
            assertThrows(IllegalArgumentException.class, () -> {
                new ModernProtocolHandler.DeviceStatus(
                    "",
                    true,
                    Instant.now(),
                    39.9042,
                    116.4074,
                    85,
                    ModernProtocolHandler.DeviceType.GPS_TRACKER
                );
            });
            
            logger.info("无效设备ID验证通过");
        }
        
        @Test
        @DisplayName("无效纬度测试")
        void testInvalidLatitude() {
            // 测试无效纬度
            assertThrows(IllegalArgumentException.class, () -> {
                new ModernProtocolHandler.DeviceStatus(
                    "GT06-DEVICE-002",
                    true,
                    Instant.now(),
                    91.0, // 无效纬度
                    116.4074,
                    85,
                    ModernProtocolHandler.DeviceType.GPS_TRACKER
                );
            });
            
            logger.info("无效纬度验证通过");
        }
        
        @Test
        @DisplayName("无效经度测试")
        void testInvalidLongitude() {
            // 测试无效经度
            assertThrows(IllegalArgumentException.class, () -> {
                new ModernProtocolHandler.DeviceStatus(
                    "GT06-DEVICE-003",
                    true,
                    Instant.now(),
                    39.9042,
                    181.0, // 无效经度
                    85,
                    ModernProtocolHandler.DeviceType.GPS_TRACKER
                );
            });
            
            logger.info("无效经度验证通过");
        }
        
        @Test
        @DisplayName("设备类型描述测试")
        void testDeviceTypeDescriptions() {
            // 测试所有设备类型的描述
            assertEquals("GPS定位器", ModernProtocolHandler.DeviceType.GPS_TRACKER.getDescription());
            assertEquals("车载监控", ModernProtocolHandler.DeviceType.VEHICLE_MONITOR.getDescription());
            assertEquals("资产跟踪器", ModernProtocolHandler.DeviceType.ASSET_TRACKER.getDescription());
            assertEquals("个人跟踪器", ModernProtocolHandler.DeviceType.PERSONAL_TRACKER.getDescription());
            
            logger.info("设备类型描述测试通过");
        }
    }
    
    @Nested
    @DisplayName("批量处理测试")
    class BatchProcessingTests {
        
        @Test
        @DisplayName("批量协议包处理测试")
        void testBatchPacketProcessing() {
            // 创建多个协议包
            LoginPacket loginPacket = new LoginPacket();
            loginPacket.setDeviceId("BATCH-001");
            loginPacket.setProtocolNumber((byte) 0x01);
            
            LocationPacket locationPacket = new LocationPacket();
            locationPacket.setDeviceId("BATCH-002");
            locationPacket.setProtocolNumber((byte) 0x12);
            locationPacket.setLatitude(39.9042);
            locationPacket.setLongitude(116.4074);
            
            HeartbeatPacket heartbeatPacket = new HeartbeatPacket();
            heartbeatPacket.setDeviceId("BATCH-003");
            heartbeatPacket.setProtocolNumber((byte) 0x13);
            
            // 批量处理
            assertDoesNotThrow(() -> {
                handler.processBatchPackets(loginPacket, locationPacket, heartbeatPacket);
            });
            
            logger.info("批量协议包处理测试通过");
        }
    }
    
    @Nested
    @DisplayName("JDK21特性演示测试")
    class JDK21FeaturesTests {
        
        @Test
        @DisplayName("JDK21特性演示测试")
        void testJDK21FeaturesDemo() {
            // 测试JDK21特性演示
            assertDoesNotThrow(() -> {
                handler.demonstrateJDK21Features();
            });
            
            logger.info("JDK21特性演示测试通过");
        }
        
        @Test
        @DisplayName("文本块特性测试")
        void testTextBlocks() {
            // 使用文本块
            String expectedInfo = """
                这是一个多行文本块测试
                支持换行和格式化
                非常适合JSON、SQL、HTML等内容
                """;
            
            assertNotNull(expectedInfo);
            assertTrue(expectedInfo.contains("多行文本块"));
            assertTrue(expectedInfo.contains("换行和格式化"));
            
            logger.info("文本块特性测试通过");
        }
        
        @Test
        @DisplayName("Switch表达式测试")
        void testSwitchExpressions() {
            // 测试Switch表达式
            ModernProtocolHandler.DeviceType deviceType = ModernProtocolHandler.DeviceType.GPS_TRACKER;
            
            String category = switch (deviceType) {
                case GPS_TRACKER, PERSONAL_TRACKER -> "个人设备";
                case VEHICLE_MONITOR -> "车载设备";
                case ASSET_TRACKER -> "资产设备";
            };
            
            assertEquals("个人设备", category);
            logger.info("Switch表达式测试通过: {}", category);
        }
        
        @Test
        @DisplayName("记录类模式匹配测试")
        void testRecordPatternMatching() {
            // 创建处理结果
            ModernProtocolHandler.ProcessResult result = 
                new ModernProtocolHandler.ProcessResult.Success("测试成功", Instant.now());
            
            // 使用模式匹配
            String message = switch (result) {
                case ModernProtocolHandler.ProcessResult.Success(var msg, var time) -> 
                    "成功: " + msg + " (时间: " + time + ")";
                case ModernProtocolHandler.ProcessResult.Failure(var error, var cause, var time) -> 
                    "失败: " + error;
                case ModernProtocolHandler.ProcessResult.Warning(var warning, var suggestion, var time) -> 
                    "警告: " + warning;
            };
            
            assertTrue(message.contains("成功"));
            assertTrue(message.contains("测试成功"));
            
            logger.info("记录类模式匹配测试通过: {}", message);
        }
    }
    
    @Test
    @DisplayName("性能基准测试")
    void testPerformanceBenchmark() {
        // 创建测试数据
        LoginPacket[] packets = new LoginPacket[1000];
        for (int i = 0; i < packets.length; i++) {
            LoginPacket packet = new LoginPacket();
            packet.setDeviceId("PERF-TEST-" + i);
            packet.setProtocolNumber((byte) 0x01);
            packets[i] = packet;
        }
        
        // 性能测试
        long startTime = System.nanoTime();
        
        for (LoginPacket packet : packets) {
            handler.processPacket(packet);
        }
        
        long endTime = System.nanoTime();
        long duration = Duration.ofNanos(endTime - startTime).toMillis();
        
        logger.info("处理1000个协议包耗时: {} ms", duration);
        logger.info("平均每个协议包处理时间: {} μs", (endTime - startTime) / packets.length / 1000);
        
        // 验证性能要求（每个包处理时间应该小于1ms）
        assertTrue(duration < 1000, "批量处理性能不符合要求");
    }
}