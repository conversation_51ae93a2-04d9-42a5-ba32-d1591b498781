@echo off
chcp 65001 >nul

REM GT06协议服务器 JDK21 Windows启动脚本
REM 使用JDK21的新特性和优化参数

echo === GT06协议服务器 JDK21版本启动脚本 ===
echo 启动时间: %date% %time%

REM 检查JDK版本
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)
set JAVA_VERSION=%JAVA_VERSION:"=%
echo 当前Java版本: %JAVA_VERSION%

REM JDK21 JVM参数配置
set JVM_OPTS=

REM 内存配置
set JVM_OPTS=%JVM_OPTS% -Xms512m
set JVM_OPTS=%JVM_OPTS% -Xmx2g

REM 使用ZGC垃圾收集器（JDK21推荐）
set JVM_OPTS=%JVM_OPTS% -XX:+UseZGC
set JVM_OPTS=%JVM_OPTS% -XX:+UnlockExperimentalVMOptions

REM 启用虚拟线程（JDK21特性）
set JVM_OPTS=%JVM_OPTS% --enable-preview

REM 性能优化参数
set JVM_OPTS=%JVM_OPTS% -XX:+UseCompressedOops
set JVM_OPTS=%JVM_OPTS% -XX:+UseCompressedClassPointers
set JVM_OPTS=%JVM_OPTS% -XX:+OptimizeStringConcat

REM 网络优化
set JVM_OPTS=%JVM_OPTS% -Djava.net.preferIPv4Stack=true
set JVM_OPTS=%JVM_OPTS% -Djava.awt.headless=true

REM 监控和诊断
set JVM_OPTS=%JVM_OPTS% -XX:+HeapDumpOnOutOfMemoryError
set JVM_OPTS=%JVM_OPTS% -XX:HeapDumpPath=.\logs\
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCDetails
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCTimeStamps
set JVM_OPTS=%JVM_OPTS% -Xloggc:.\logs\gc.log

REM JFR（Java Flight Recorder）配置
set JVM_OPTS=%JVM_OPTS% -XX:+FlightRecorder
set JVM_OPTS=%JVM_OPTS% -XX:StartFlightRecording=duration=60s,filename=.\logs\flight-recording.jfr

REM 应用程序参数
set APP_OPTS=
set APP_OPTS=%APP_OPTS% -Dspring.profiles.active=jdk21
set APP_OPTS=%APP_OPTS% -Dfile.encoding=UTF-8
set APP_OPTS=%APP_OPTS% -Duser.timezone=Asia/Shanghai

REM 类路径配置
set CLASSPATH=target\classes;target\lib\*

REM 主类
set MAIN_CLASS=com.gt06.Application

REM 服务器端口（可通过参数覆盖）
if "%1"=="" (
    set SERVER_PORT=8841
) else (
    set SERVER_PORT=%1
)

echo JVM参数: %JVM_OPTS%
echo 应用参数: %APP_OPTS%
echo 服务器端口: %SERVER_PORT%
echo 主类: %MAIN_CLASS%
echo.

REM 创建日志目录
if not exist logs mkdir logs

REM 启动应用
echo 正在启动GT06协议服务器...
java %JVM_OPTS% %APP_OPTS% -cp %CLASSPATH% %MAIN_CLASS% %SERVER_PORT%

REM 检查启动结果
if %ERRORLEVEL% EQU 0 (
    echo GT06协议服务器启动成功！
    echo 服务器端口: %SERVER_PORT%
    echo 管理控制台: http://localhost:8842
    echo 日志文件: .\logs\gt06-server.log
) else (
    echo GT06协议服务器启动失败！
    echo 请检查日志文件: .\logs\gt06-server.log
    pause
    exit /b 1
)

pause