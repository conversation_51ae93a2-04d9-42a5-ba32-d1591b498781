#!/bin/bash

# GT06协议服务器 JDK21 启动脚本
# 使用JDK21的新特性和优化参数

echo "=== GT06协议服务器 JDK21版本启动脚本 ==="
echo "启动时间: $(date)"

# 检查JDK版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "当前Java版本: $JAVA_VERSION"

if [[ ! "$JAVA_VERSION" =~ ^21\. ]]; then
    echo "警告: 建议使用JDK21运行此应用以获得最佳性能"
fi

# JDK21 JVM参数配置
JVM_OPTS=""

# 内存配置
JVM_OPTS="$JVM_OPTS -Xms512m"
JVM_OPTS="$JVM_OPTS -Xmx2g"

# 使用ZGC垃圾收集器（JDK21推荐）
JVM_OPTS="$JVM_OPTS -XX:+UseZGC"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"

# 启用虚拟线程（JDK21特性）
JVM_OPTS="$JVM_OPTS --enable-preview"

# 性能优化参数
JVM_OPTS="$JVM_OPTS -XX:+UseCompressedOops"
JVM_OPTS="$JVM_OPTS -XX:+UseCompressedClassPointers"
JVM_OPTS="$JVM_OPTS -XX:+OptimizeStringConcat"

# 网络优化
JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"
JVM_OPTS="$JVM_OPTS -Djava.awt.headless=true"

# 监控和诊断
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=./logs/"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -Xloggc:./logs/gc.log"

# JFR（Java Flight Recorder）配置
JVM_OPTS="$JVM_OPTS -XX:+FlightRecorder"
JVM_OPTS="$JVM_OPTS -XX:StartFlightRecording=duration=60s,filename=./logs/flight-recording.jfr"

# 应用程序参数
APP_OPTS=""
APP_OPTS="$APP_OPTS -Dspring.profiles.active=jdk21"
APP_OPTS="$APP_OPTS -Dfile.encoding=UTF-8"
APP_OPTS="$APP_OPTS -Duser.timezone=Asia/Shanghai"

# 类路径配置
CLASSPATH="target/classes:target/lib/*"

# 主类
MAIN_CLASS="com.gt06.Application"

# 服务器端口（可通过参数覆盖）
SERVER_PORT=${1:-8841}

echo "JVM参数: $JVM_OPTS"
echo "应用参数: $APP_OPTS"
echo "服务器端口: $SERVER_PORT"
echo "主类: $MAIN_CLASS"
echo ""

# 创建日志目录
mkdir -p logs

# 启动应用
echo "正在启动GT06协议服务器..."
java $JVM_OPTS $APP_OPTS -cp $CLASSPATH $MAIN_CLASS $SERVER_PORT

# 检查启动结果
if [ $? -eq 0 ]; then
    echo "GT06协议服务器启动成功！"
    echo "服务器端口: $SERVER_PORT"
    echo "管理控制台: http://localhost:8842"
    echo "日志文件: ./logs/gt06-server.log"
else
    echo "GT06协议服务器启动失败！"
    echo "请检查日志文件: ./logs/gt06-server.log"
    exit 1
fi