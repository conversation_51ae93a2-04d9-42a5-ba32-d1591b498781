# GT06协议服务器环境配置指南

## 🚨 当前问题诊断

### 发现的问题
1. **JDK版本不匹配**：项目需要JDK21，当前环境为JDK8
2. **Maven环境未配置**：无法执行mvn命令
3. **pom.xml配置错误**：已修复XML标签问题

### 环境检查结果
```
当前JDK版本: OpenJDK 1.8.0_442
项目要求版本: JDK 21 + --enable-preview
Maven状态: 未安装或未配置PATH
```

## 📋 解决方案

### 方案1：安装JDK21环境（推荐）

#### 1. 下载并安装JDK21
```bash
# 推荐使用Oracle JDK 21或OpenJDK 21
# 下载地址：
# Oracle JDK: https://www.oracle.com/java/technologies/downloads/#java21
# OpenJDK: https://jdk.java.net/21/
```

#### 2. 配置环境变量
```cmd
# 设置JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-21

# 更新PATH
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证安装
java -version
javac -version
```

#### 3. 安装Maven
```bash
# 下载Maven 3.9+
# 下载地址：https://maven.apache.org/download.cgi

# 配置环境变量
set MAVEN_HOME=C:\apache-maven-3.9.5
set PATH=%MAVEN_HOME%\bin;%PATH%

# 验证安装
mvn -version
```

### 方案2：使用便携式环境

#### 创建便携式启动脚本
```cmd
@echo off
echo 正在检查Java环境...

# 检查JDK21
java -version 2>&1 | findstr "21" >nul
if %errorlevel% neq 0 (
    echo 错误：需要JDK21环境
    echo 请安装JDK21或使用便携式JDK
    pause
    exit /b 1
)

# 检查Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：Maven未安装
    echo 请安装Maven或配置PATH环境变量
    pause
    exit /b 1
)

echo 环境检查通过，开始编译...
mvn clean compile
```

## 🔧 编译和运行

### 编译项目
```bash
# 清理并编译
mvn clean compile

# 运行测试
mvn test

# 打包
mvn clean package
```

### 运行项目
```bash
# 使用Maven运行
mvn exec:java -Dexec.mainClass="com.gt06.Application"

# 或使用JAR包运行
java --enable-preview -jar target/gt06-protocol-server-2.1.0.jar
```

## ⚠️ 重要说明

### JDK21特性使用
项目使用了以下JDK21特性，必须在JDK21环境下运行：
- StructuredTaskScope（结构化并发）
- sealed类（密封类）
- record类（记录类）
- 虚拟线程
- switch表达式

### 预览特性
项目使用了JDK21的预览特性，需要添加`--enable-preview`参数：
```bash
java --enable-preview -cp target/classes com.gt06.Application
```

## 🔍 故障排除

### 常见问题
1. **编译错误**：确保使用JDK21并启用预览特性
2. **运行时错误**：检查JVM参数是否包含`--enable-preview`
3. **依赖问题**：运行`mvn dependency:resolve`检查依赖

### 验证命令
```bash
# 检查Java版本
java -version

# 检查编译器版本
javac -version

# 检查Maven版本
mvn -version

# 测试编译
mvn clean compile -X
```
